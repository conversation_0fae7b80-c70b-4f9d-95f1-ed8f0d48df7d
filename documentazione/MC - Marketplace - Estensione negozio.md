# 

# 

# **MARKETPLACE \- ESTENSIONE NEGOZIO**

**Ver. 1.0 del 13/11/2024**  
**Rev.1.1 del 12/04/2025**  
**INDICE GENERALE**

[Introduzione](#introduzione)

[Indicazioni specifiche di comportamento](#indicazioni-specifiche-di-comportamento)

[Comportamenti Specifici](#comportamenti-specifici)

[Interfacce](#interfacce)

[Modifiche lato interfaccia genitore sito/app:](#modifiche-lato-interfaccia-genitore-sito/app:)

[Modifiche lato interfaccia mastercom didattica:](#modifiche-lato-interfaccia-mastercom-didattica:)

[API](#api)

[API necessarie per l'operatività completa:](#api-necessarie-per-l'operatività-completa:)

[API esistenti e realizzate](#api-esistenti-e-realizzate)

[inserisci\_marketplace](#inserisci_marketplace)

[esempio di caratteristiche per un servizio ad adesione giornaliera:](#esempio-di-caratteristiche-per-un-servizio-ad-adesione-giornaliera:)

[esempio di caratteristiche dell'oggetto del negozio con tutti i campi possibili](#esempio-di-caratteristiche-dell'oggetto-del-negozio-con-tutti-i-campi-possibili)

[esempio di caratteristiche del servizio extra time con tutti i campi possibili, attualmente attivo solo su Myschool Ticino:](#esempio-di-caratteristiche-del-servizio-extra-time-con-tutti-i-campi-possibili,-attualmente-attivo-solo-su-myschool-ticino:)

[elenco acquisti (estrai lista oggetti acquistati)](#elenco-acquisti-\(estrai-lista-oggetti-acquistati\))

[Struttura del DB e relative modifiche](#struttura-del-db-e-relative-modifiche)

[Struttura DB](#struttura-db)

[Query SQL x MasterCom](#query-sql-x-mastercom)

# Introduzione {#introduzione}

In questa documentazione verra’ riportata la logica di funzionamento, la struttura e le considerazioni di sviluppo per l’estensione dell’attuale struttura marketplace al fine di riuscire ad implementare un vero e proprio negozio on line attraverso il quale gli istituti possano vendere e gestire i conseguenti ordini di tutto ciò che desiderano.

# 

# Indicazioni specifiche di comportamento {#indicazioni-specifiche-di-comportamento}

Attualmente il json del campo caratteristiche della tabella marketplace contiene o null o una struttura dedicata ai servizi giornalieri di cui riporto un esempio qua sotto:

```json
{
  "adesione_giornaliera": "SI",
  "orario_limite_inserimento": "14:00",
  "orario_limite_cancellazione": "14:00",
  "giustificazione_attiva": "SI",
  "notifica_assenze": "SI",
  "giornate_attive": {
    "lun": "NO",
    "mar": "NO",
    "mer": "NO",
    "gio": "NO",
    "ven": "SI",
    "sab": "NO",
    "dom": "NO"
  }
}
```

Unica scuola che implementa una struttura aggiuntiva e molto personalizzata di caratteristiche è MySchool Ticino la cui struttura è visibile nell’esempio riportato [qui](#esempio-di-caratteristiche-del-servizio-extra-time-con-tutti-i-campi-possibili,-attualmente-attivo-solo-su-myschool-ticino:)

A queste possibili strutture sarà aggiunto un nuovo elemento che potrà essere presente in modo esclusivo nel JSON.

All’interno del nuovo oggetto\_negozio saranno presenti diversi campi, tra cui il campo "inserire\_movimento\_immediatamente" che se valorizzato a SI indica il fatto che appena eseguito l'ordine da parte del cliente, questo viene immediatamente contabilizzato inserendo il movimento in MC2, negli altri casi deve attendere che l'ordine venga processato e contabilizzato in una fase successiva (ad esempio dopo averne verificato la disponibilità), questo serve per poter gestire oggetti tipo i buoni pasto che devono essere contabilizzati immediatamente e che non sono soggetti ad esaurimento disponibilità.  
I restanti campi principali sono autoesplicativi, particolare attenzione meritano le strutture, qui sotto vediamo espansa la struttura "dettaglio\_ordine\_multiplo", l'utilizzo di questa struttura è vincolato al popolamento del campo "consenti\_ordine\_multiplo" con il valore "SI".  
Tale struttura indica le modalità, in termini quantitativi, di acquisto per un singolo ordine:

* "num\_min" determina la quantità minima ordinabile  
* "num\_max" la quantità massima ordinabile  
* "a\_blocchi\_di" indica il multiplo ammissibile per l'ordine

Nell'esempio sotto le quantità ordinabili saranno: 5,10,15,20...90,95,100

```json
{
  "oggetto_negozio": {
    "valuta": "euro",
    "prezzo_unitario": "10.50",
    "prezzo_scontato": "9.90",
    "sconto_valido_dal": "15/12/2023",
    "sconto_valido_al": "15/12/2023",
    "inserire_movimento_immediatamente": "SI",
    "tipo_pagamento_disponibile": "online",
    "consenti_ordine_multiplo": "NO",
    "dettagli_ordine_multiplo": {
      "num_min": "1",
      "num_max": "100",
      "a_blocchi_di": "5"
    },
    "caratteristica": {},
    "immagini": {},
    "oggetto_disponibile_per": "solo_selezionati",
    "disponibile_per_indirizzi": {},
    "disponibile_per_classi": {}
  }
}
```

Qui di seguito sono espansi per maggiore chiarezza i dettagli della caratteristica dell'oggetto in questione.  
Se la caratteristica non è popolata (non viene utilizzata) avrà valore null.  
La caratteristica ha la possibilità di avere disponibili fino a 10 valori, i valori non compilati dovranno essere impostati a null.  
Il campo scelta obbligatoria indica che tale caratteristica DEVE essere selezionata dall'acquirente altrimenti non è possibile inviare l'ordine

```json
"caratteristica": {
  "descrizione": "Taglia",
  "scelta_obbligatoria": "SI",
  "valori_possibili": {
    "1": {
      "label": "XS",
      "disponibili": 4
    },
    "2": {
      "label": "Small",
      "disponibili": 8
    },
    "3": {
      "label": "Medium",
      "disponibili": 3
    },
    "4": {
      "label": "Large",
      "disponibili": 9
    },
    "5": {
      "label": "XL",
      "disponibili": 4
    },
    "6": {
      "label": "XXL",
      "disponibili": 12
    },
    "7": null
  }
}
```

Discorso analogo per le immagini allegabili all'oggetto, sono previste fino ad un massimo di 3 immagini.   
Se un'immagine non è popolata avrà valore null.

```json
"immagini": {
  "1": {
    "file_name": "fronte",
    "file_type": "png",
    "file_mime": "image/png",
    "file_size": "67906",
    "rootname": "attachment_1688047698.9927dca31f99"
  },
  "2": {
    "file_name": "retro",
    "file_type": "png",
    "file_mime": "image/png",
    "file_size": "75912",
    "rootname": "attachment_1688048091.7855c6848a03"
  },
  "3": null
}
```

Per avere completa libertà sulla visualizzazione dell'oggetto sarà possibile definire se è visibile da tutti o se sarà visibile solo per gli indirizzi/classi attivate.Di seguito i due esempi possibili

```json
{
  "oggetto_disponibile_per": "solo_selezionati",
  "disponibile_per_indirizzi": {
    "100202": 100202,
    "100203": 100203
  },
  "disponibile_per_classi": {
    "100437": 100437,
    "100489": 100489,
    "100523": 100523
  }
}
```

Tutte le considerazioni sopra sono propedeutiche per la comprensione del nuovo campo "stato\_ordine" inserito in "marketplace\_studenti\_acquisti".

Tale campo avrà sostanzialmente tre valori per tutti quei record i cui padri contengano nelle caratteristiche il record valorizzato "oggetto\_negozio":

* ORDINATO \-\> indica che l'oggetto aveva il campo "inserire\_movimento\_immediatamente" valorizzato a NO indicando così il fatto che l'oggetto non può essere acquistato senza una conferma da parte della scuola.  
* CONFERMATO \-\> indica che la scuola ha accettato l'ordine e considera perfezionato l'acquisto, viene generato il movimento in MC2 e da questo momento il cliente può procedere a pagare l'oggetto acquistato tramite i canali previsti.  
* EVASO \-\> indica che l'ordine è stato evaso e il materiale consegnato al genitore  
* CARRELLO \-\> indica che l’elemento e’ stato inserito nel carrello dall’utente. Serve per salvare appunto lo stato del carrello tra le varie sessioni. Ogni volta che questo verra’ aperto, bisognera’ controllare e aggiornare le disponibilita’.  NB: Nella maggior parte delle estrazioni questo stato dovra’ essere filtrato, valutare se filtrarlo sempre di default ed estrarne gli elementi solo su richiesta.

# 

# **Comportamenti Specifici** {#comportamenti-specifici}

Gli oggetti possono avere combinazioni di impostazioni differenti che ne determinano il comportamento quando un genitore effettua un acquisto di un oggetto:

Lo stato di partenza è sempre ‘CARRELLO’, in quanto per essere ordinato, pagato ecc, l’”oggetto” deve essere prima stato inserito nel carrello. Da li’ poi potra’ prendere le strade differenti in base a se si puo’ pagare subito o se andra’ prima confermato dalla segreteria.

Se il campo "inserire\_movimento\_immediatamente" è valorizzato a "SI", il campo "stato\_ordine" di "marketplace\_studenti\_acquisti" viene sempre valorizzato a EVASO e contestualmente viene inserito in automatico il movimento su MC2 e a questo punto si distinguono due casi:

* caso in cui il campo "tipo\_pagamento\_previsto" sia impostato a "online" nella fase di checkout il genitore dovrà procedere al pagamento tramite i metodi on line messi a disposizione dalla scuola (carte di credito/satispay/nuove opportunità) e al termine di questa operazione viene inserito in automatico in MC2 anche il relativo pagamento e viene chiusa la partita.

* caso in cui il campo "tipo\_pagamento\_previsto" sia impostato a "predefinito" nella fase di checkout il genitore NON dovrà procedere al pagamento, ma semplicemente confermare l'acquisto, procederà con i canali previsti al pagamento dalla scuola per lui (SEPA, bonifico, rimessa diretta, ecc.)

Se il campo "inserire\_movimento\_immediatamente" NON è valorizzato a "SI", il campo "stato\_ordine" di "marketplace\_studenti\_acquisti" viene sempre valorizzato a ORDINATO e non viene inserito nulla in MC2. Finché "stato\_ordine" rimane uguale a ORDINATO il cliente può procedere ad annullarlo, appena cambia stato non potrà più fare nulla in autonomia.

Quando "stato\_ordine" passa da ORDINATO a CONFERMATO o EVASO viene inserito in automatico in MC2 il movimento relativo e a questo punto si evidenziano anche qui due possibili scenari:

* caso in cui il campo "tipo\_pagamento\_previsto" sia impostato a "online" il genitore dovrà procedere al pagamento tramite i metodi on line messi a disposizione dalla scuola (carte di credito/satispay/nuove opportunità) e al termine di questa operazione viene inserito in automatico in MC2 il relativo pagamento e viene chiusa la partita.

* caso in cui il campo "tipo\_pagamento\_previsto" sia impostato a "predefinito" il genitore procederà con i canali previsti al pagamento dalla scuola per lui (SEPA, bonifico, rimessa diretta, ecc.)

**Quanto detto sopra è riassunto e schematizzato in un diagramma di flusso nella bacheca relativa di Clickup e rappresentato nelle prossime pagine in formato testo:**

**Processo “marketplace\_studenti\_acquisti”:**

**1\. Evento innescato da “Comportamenti Specifici”:**

   **└→ parametro: inserire\_movimento\_immediatamente?**

**2\. Se inserire\_movimento\_immediatamente \= SÌ:**

   **2.1. Aggiorna tabella marketplace\_studenti\_acquisti:**

        **stato\_ordine → “EVASO”**

   **2.2. Inserisci movimento in MC2**

   **2.3. Esegui checkout ordine**

   **2.4. Controlla parametro tipo\_pagamento\_previsto:**

        **├─ se “on line”:**

        **│    a) Effettua pagamento tramite canali online (carte di credito, Satispay, ecc.)**

        **│    b) Inserisci il pagamento in MC2**

        **└─ se “predefinito”:**

             **• Il pagamento avverrà con i canali tradizionali previsti dalla scuola**

               **(SEPA, bonifico, ecc.)**

**3\. Se inserire\_movimento\_immediatamente ≠ SÌ:**

   **3.1. Aggiorna tabella marketplace\_studenti\_acquisti:**

        **stato\_ordine → “ORDINATO”**

   **3.2. L’ordine rimane modificabile finché è in stato “ORDINATO”**

   **3.3. La segreteria verifica la disponibilità dell’oggetto ordinato**

   **3.4. Se l’oggetto è disponibile:**

        **a) Aggiorna tabella marketplace\_studenti\_acquisti:**

           **stato\_ordine → “CONFERMATO”**

        **b) Inserisci movimento in MC2**

   **3.5. Se l’oggetto NON è disponibile:**

        **• La segreteria decide se:**

          **– Annullare l’ordine (stato → “ANNULLATO”)**  

          **– Oppure attendere di poterlo evadere più avanti**  

   **3.6. In entrambi i casi:**

        **• Inserire nel record dell’ordine un messaggio di notifica** 

          **(campo interno) o inviare una notifica via messenger**

# 

# **Comunicazioni**

Ogni cambio di stato dell’ordine o altra comunicazione verra’ effettuata tramite messaggi Messenger, cosi’ che la parte di notifiche sia gia’ presente e funzionale. 

**gruppo ‘NEGOZIO’**  
Prevedere un gruppo dedicato alle comunicazione del negozio abbinabile dalla interfaccia di amministrazione. A tale gruppo arriveranno le comunicazioni inerenti agli ordini in ingresso, in modo che più utenti possano riceverle (i membri del gruppo) e possano scambiare anche messaggi con i clienti, effettuando a tutti gli effetti customers care. 

# 

# **Interfacce** {#interfacce}

## **Modifiche lato interfaccia genitore sito/app:** {#modifiche-lato-interfaccia-genitore-sito/app:}

Deve essere realizzata una sezione Negozio all’interno della quale dovranno essere visualizzati tutti gli oggetti disponibili tutti inseribili nel carrello.

Dovrà esserci una nuova sezione Ordini a livello di quella del Negozio all’interno della quale si dovranno poter vedere e gestire gli ordini.

Si potrebbe fare una sezione(tab?) Ordini Storici dove vanno a finire tutti gli ordini confermati e pagati e una Ordini Attivi in cui si fanno vedere tutti gli ordini che sono ancora da confermare e/o da pagare, in pratica gli ordini in cui l’utente può compiere azioni operative. In Attivi vanno tutti gli ordini che hanno il campo "stato\_ordine" della tabella "marketplace\_studenti\_acquisti" al valore ORDINATO o CONFERMATO ma ancora da pagare.

![][image1]

## **Modifiche lato interfaccia mastercom didattica:** {#modifiche-lato-interfaccia-mastercom-didattica:}

Nella sezione AMMIN sarà necessario ristrutturare la creazione dei servizi facendo 3 sezioni specifiche, la prima per la gestione dei servizi base, la seconda per i servizi giornalieri e la terza per gli oggetti da far vedere nel negozio.

Queste divisioni dovranno fare capo alle righe di marketplace con categoria di tipo:

* STANDARD \=\> servizi base  
* SERVIZI\_GIORNALIERI \=\> servizi giornalieri  
* NEGOZIO \=\> oggetti messi a listino per il negozio  
* CONSOLIDAMENTO\_MENSA \=\> sezione dedicata per il consolidamento della mensa che avviene in MC2  (magari da collegare alla sezione di setup della mensa??)  
* EXTRATIME \=\> sezione personalizzata e dedicata a Myschool

Sempre nella sezione AMMIN sarà necessario inserire una sezione di gestione degli ordini che permetta di vedere la lista degli ordini arrivati/confermati/evasi su cui fare le operazioni di cambio stato.

Elenco variazione degli stati possibili:

* Da stato ORDINATO a CONFERMATO dovrà generare il movimento MC2 relativo e inviare una mail in cui viene comunicata la conferma dell'ordine e che per essere definitivamente evaso è in attesa del pagamento (tipo di pagamento secondo quanto previsto dall'oggetto stesso)  
* Da stato ORDINATO ad EVASO dovrà generare il movimento MC2 relativo e inviare una mail al genitore con la conferma dell'accettazione e la possibilità di effettuare il pagamento (tipo di pagamento secondo quanto previsto dall'oggetto stesso).  
* Da stato CONFERMATO ad EVASO dovrà mandare una mail in cui comunica che l'ordine è stato evaso/consegnato.

![][image2]

# **API** {#api}

## **API necessarie per l'operatività completa:** {#api-necessarie-per-l'operatività-completa:}

* inserisci\_marketplace (inserisce un elemento anche di tipo negozio in marketplace)  
  * autorizzazioni: solo amministratori e sadmin  
* inserisci acquisto marketplace (inserisce l'acquisto del servizio/oggetto negozio con le opzioni scelte)  
  * autorizzazioni: sadmin, amministratori (per chiunque) e genitori (solo per i propri figli)  
* \<NEW\> cambia stato acquisto (permette di passare da uno stato ad un altro l'acquisto)  
  * autorizzazioni: sadmin, amministratori (per chiunque) e genitori (solo per i propri ordini)  
* \<NEW\> estrai lista oggetti negozio (estrae la lista degli oggetti acquistabili in base ai vari limiti/parametri inseriti negli specifici oggetti e in base allo studente)  
  * autorizzazioni: genitori (specificamente per i propri figli)  
* \<NEW\> estrai lista oggetti acquistati (estrae la lista degli oggetti acquistati o ordinati in base al figlio in esame)  
  * autorizzazioni: genitori (specificamente per i propri ordini effettuati)  
* \<NEW\> inserisci articolo nel carrello (aggiunge una riga markeplace\_studenti\_acquisti con stato ‘CARRELLO’)  
  * autorizzazioni: genitori (specificamente per ogni figlio)  
* \<NEW\> elimina articolo nel carrello (elimina la riga markeplace\_studenti\_acquisti con stato ‘CARRELLO’)  
  * autorizzazioni: genitori (specificamente per ogni figlio)  
* \<NEW\> estrai articoli carrello (estrae gli articoli del carrello, quindi le righe con stato ‘CARRELLO’ di marketplace\_studenti\_acquisti del figlio che sto visualizzando)  
  * autorizzazioni: genitori (specificamente per ogni figlio)  
* \<NEW\> verifica disponibilita’ articoli carrello (da chiamare prima di un ordine/pagamento per verificare che sia ancora disponibile e non sia finito nell’arco di tempo tra l’inserimento nel carrello e il checkout)  
  * autorizzazioni: genitori (specificamente per ogni figlio)

## 

## **API esistenti e realizzate** {#api-esistenti-e-realizzate}

### **inserisci\_marketplace** {#inserisci_marketplace}

{{url}}/marketplace/inserisci

Funzione in POST

* inserisce un nuovo elemento in marketplace sulla base dei dati passati in $dati i dati utilizzati sono:  
  * $dati\['descrizione'\] deve contenere la descrizione del servizio \- varchar \- obbligatorio  
  * $dati\['db\_richiesto'\] deve contenere il nome completo del database su cui effettuare le modifiche  
  * $dati\['tipo'\] deve contenere il tipo del servizio (es. mensa) \- varchar \- obbligatorio  
  * $dati\['id\_tipo\_movimento'\] deve contenere l'id del tipo di movimento di MC2 da abbinare all'articolo  
  * $dati\['codice'\] deve contenere il codice di collegamento al modulo delle preiscrizione per poterlo abbinare  
  * $dati\['ordinamento'\] deve contenere la stringa di tre caratteri per poterla ordinare, default 000  
  * $dati\['nome\_sitoapp'\] deve contenere la stringa del nome da presentare sul sito genitori  
  * $dati\['descrizione\_sitoapp'\] deve contenere la stringa per la descrizione da presentare sul sito genitori  
  * $dati\['pubblica\_sitoapp'\] deve contenere il valore SI/NO per indicare se deve essere un servizio pubblicato o meno sul sito dei genitori  
  * $dati\['categoria'\] deve contenere il valore STANDARD/NEGOZIO per indicare se deve essere un servizio standard o se deve essere un servizio da presentare nella sezione negozio e quindi acquistabile  
  * $dati\['validita\_inizio'\] deve contenere in timestamp la data di inizio validità  del servizio  
  * $dati\['validita\_fine'\] deve contenere in timestamp la data di fine validità del servizio  
  * $dati\['caratteristiche'\] deve contenere il json delle caratteristiche specifiche dell'articolo (es: ripetibilità , frequenza (mensile, settimanale, ecc.))


####      esempio di caratteristiche per un servizio ad adesione giornaliera: {#esempio-di-caratteristiche-per-un-servizio-ad-adesione-giornaliera:}

           {  
               "adesione\_giornaliera": "SI",  
               "orario\_limite\_inserimento": "16:30",  
               "orario\_limite\_cancellazione": "16:30",  
               "giustificazione\_attiva": "SI",  
               "notifica\_assenze": "SI",  
               "giornate\_attive": {  
                   "lun": "SI",  
                   "mar": "NO",  
                   "mer": "NO",  
                   "gio": "NO",  
                   "ven": "NO",  
                   "sab": "NO",  
                   "dom": "NO"  
               }  
           }  
     

#### esempio di caratteristiche dell'oggetto del negozio con tutti i campi possibili {#esempio-di-caratteristiche-dell'oggetto-del-negozio-con-tutti-i-campi-possibili}

      {  
           "oggetto\_negozio": {  
               "valuta": "euro",  
               "prezzo\_unitario": "10.50",  
               "prezzo\_scontato": "9.90",  
               "sconto\_valido\_dal": "15/12/2023",  
               "sconto\_valido\_al": "15/12/2023",  
               "inserire\_movimento\_immediatamente": "SI",  
               "tipo\_pagamento\_disponibile": "online",  
               "consenti\_ordine\_multiplo": "NO",  
               "dettagli\_ordine\_multiplo": {  
                   "num\_min": "1",  
                   "num\_max": "100",  
                   "a\_blocchi\_di": "5"  
               },  
           "caratteristica": {  
                   "descrizione": "Taglia",  
                   "scelta\_obbligatoria": "SI",  
                   "valori\_possibili": {  
                       "1": {  
                         "label": "XS",  
                         "disponibili": 4  
                         },  
                       "2": {  
                         "label": "Small",  
                         "disponibili": 8  
                         },  
                       "3": {  
                         "label": "Medium",  
                         "disponibili": 3  
                         },  
                       "4": {  
                         "label": "Large",  
                         "disponibili": 9  
                         },  
                       "5": {  
                         "label": "XL",  
                         "disponibili": 4  
                         },  
                       "6": {  
                         "label": "XXL",  
                         "disponibili": 12  
                         },  
                       "7": null,  
                       "8": null,  
                       "9": null,  
                       "10": null  
                   }  
               },  
               "immagini": {  
               "1": {  
                   "file\_name": "fronte",  
                   "file\_type": "png",  
                   "file\_mime": "image/png",  
                   "file\_size": "67906",  
                   "rootname": "attachment\_1688047698.9927dca31f99"  
               },  
               "2": {  
                   "file\_name": "retro",  
                   "file\_type": "png",  
                   "file\_mime": "image/png",  
                   "file\_size": "75912",  
                   "rootname": "attachment\_1688048091.7855c6848a03"  
               },  
               "3": null  
               },  
               "oggetto\_disponibile\_per": "solo\_selezionati",  
               "disponibile\_per\_indirizzi": {  
                   "100202": 100202,  
                   "100203": 100203  
               },  
               "disponibile\_per\_classi": {  
                   "100437": 100437,  
                   "100489": 100489,  
                   "100523": 100523  
               }  
           }  
       }

#### esempio di caratteristiche del servizio extra time con tutti i campi possibili, attualmente attivo solo su Myschool Ticino:  {#esempio-di-caratteristiche-del-servizio-extra-time-con-tutti-i-campi-possibili,-attualmente-attivo-solo-su-myschool-ticino:}

           {  
               "tipo": "EXTRA TIME",  
               "descrizione": "Abbonamento A",  
               "id\_tipo\_movimento": 69,  
               "codice": "",  
               "caratteristiche": {  
                           "periodicita": "settimanale",  
                           "num\_max\_opzioni\_selezionabili": 3,  
                           "elenco\_opzioni": {  
                                         "Lun": {  
                                                 "descrizione" : "Lunedì",  
                                                 "weekday" : 1,  
                                                 "elenco\_opzioni": {  
                                                                     "base": {  
                                                                               "descrizione" : "base",  
                                                                               "ora\_inizio": "08:00",  
                                                                               "ora\_fine": "14:00",  
                                                                               "costo\_minuto\_extratime": "5",  
                                                                               "arrotondamento\_minuti": "10"  
                                                                             },  
                                                                     "esteso": {  
                                                                               "descrizione" : "esteso",  
                                                                               "ora\_inizio": "08:00",  
                                                                               "ora\_fine": "18:00",  
                                                                               "costo\_minuto\_extratime": "10",  
                                                                               "arrotondamento\_minuti": "10"  
                                                                               }  
                                                                   }  
                                               },  
                                         "Sab": {  
                                                 "descrizione" : "Sabato",  
                                                 "weekday" : 6,  
                                                 "elenco\_opzioni": {  
                                                                     "base": {  
                                                                               "descrizione" : "base",  
                                                                               "ora\_inizio": "08:00",  
                                                                               "ora\_fine": "14:00",  
                                                                               "costo\_minuto\_extratime": "5",  
                                                                               "arrotondamento\_minuti": "10"  
                                                                             },  
                                                                     "esteso": {  
                                                                               "descrizione" : "esteso",  
                                                                               "ora\_inizio": "08:00",  
                                                                               "ora\_fine": "18:00",  
                                                                               "costo\_minuto\_extratime": "10",  
                                                                               "arrotondamento\_minuti": "10"  
                                                                               }  
                                                                   }  
                                               }  
                                       }  
                             }  
           }

esempio payload:  
{  
    "tipo": "Doposcuola 2",  
    "descrizione": "Doposcuola 2",  
    "codice": "Doposcuola 2",  
    "ordinamento": "001",  
    "descrizione\_sitoapp": "Servizio doposcuola dedicato ai ragazzi delle scuole medie",  
    "nome\_sitoapp": "Servizio Doposcuola",  
    "pubblica\_sitoapp": "SI",  
    "categoria": "NEGOZIO",  
    "validita\_inizio": 1630495233,  
    "validita\_fine": 1661984433,  
    "caratteristiche": {  
                            \<tutti i campi necessari come da esempi sopra riportati\>                             
                                }  
L'operazione è eseguibile solo da un utente amministratore o sadmin  
Restituisce l'id del marketplace inserito quando tutto a posto, gli errori riscontrati in caso contrario

### **elenco acquisti (estrai lista oggetti acquistati)** {#elenco-acquisti-(estrai-lista-oggetti-acquistati)}

{{url}}/marketplace/acquisti/

Funzione in GET

* recupera la lista degli oggetti di marketplace sulla base del filtro impostato $filter  
* i valori possibili sono:  
  * $filter\['id'\] deve contenere l'id del record da ricercare  
  * $filter\['db\_richiesto'\] deve contenere il nome completo del database su cui ricercare  
  * $filter\['id\_classe'\] deve contenere l'id della classe degli studenti da filtrare  
  * $filter\['id\_studente'\] deve contenere l'id dello studente da filtrare  
  * $filter\['id\_marketplace'\] deve contenere l'id del marketplace da filtrare  
  * $filter\['contabilizzato'\] deve contenere il valore che si intende filtrare (valori ammessi \['SI', 'NO'\])  
  * $filter\['tipo\_elementi'\] può contenere il valore che si intende filtrare (valori ammessi \['OGGETTI\_NEGOZIO', 'SOLO\_SERVIZI\_BASE', 'SOLO\_SERVIZI\_EXTRA\_TIME', 'SOLO\_SERVIZI\_GIORNALIERI', 'TUTTI\_SERVIZI', 'TUTTI'\] default 'TUTTI\_SERVIZI')  
  * $filter\['validita\_inizio'\]\['start'\] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno validita\_inizio \>= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)  
  * $filter\['validita\_inizio'\]\['end'\] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno validita\_inizio \<= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)  
  * $filter\['validita\_fine'\]\['start'\] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno validita\_fine \>= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)  
  * $filter\['validita\_fine'\]\['end'\] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno validita\_fine \<= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)  
  * $filter\['tipo\_risultato'\]   
    * se non precisato viene restituito l'array con chiave id\_acquisto di tutti gli acquisti trovati   
    * se valorizzato a "id\_studente" crea un array con chiave id\_studente per ogni studente e un sottoarray per ogni acquisto   
    * se valorizzato a "studente" crea un array con un contatore come chiave e un sottoarray per ogni acquisto   
    * se valorizzato a "dividi\_ordini\_acquisti" crea un array con chiave id\_studente e due sottoarray \['acquisti'\] e \['ordini'\] con i relativi sottoarray per ogni acquisto per stabilire   
    * se è un ordine o un acquisto viene utilizzato il campo "stato\_ordine" che se valorizzato a "ORDINATO" indica che è un ordine e negli altri casi è un acquisto  
  * $filter\['data\_adesione'\] se valorizzato va a verificare se per quella data lo studente ha aderito al servizio per ogni servizio che ha la caratteristica "adesione\_giornaliera":"SI"

esempio payload:  
{  
    "id\_studente": 1008244,  
    "tipo\_elementi": "OGGETTI\_NEGOZIO",  
	"tipo\_risultato": "dividi\_ordini\_acquistati"  
}

Se l'estrazione la sta facendo un parente viene verificato che sia effettivamente il parente dell'id\_studente di cui si sta chiedendo l'elenco degli acquisti altrimenti non estrae nulla

# **Gestione pregresso**

Per garantire una gestione ottimale dei dati al cambio di anno, è fondamentale azzerare i riferimenti ai tipi di movimento di tutti quei servizi che presentano un tipo di movimento non esistente nell’anno successivo. Questa operazione preliminare assicura che i dati storici non interferiscano con le logiche di elaborazione legate al nuovo anno.

Successivamente, sfruttando API specifiche, si dovrà provvedere a segnalare a MC2 tutti i servizi che necessitano di un aggiornamento, a seguito dell'eliminazione del tipo di movimento in vista del passaggio all'anno successivo. Questo passaggio è cruciale per garantire che MC2 possa applicare le corrette logiche economiche ai servizi interessati dal cambio di anno.

Il processo di aggiornamento si baserà su due API distinte:

* **API 1:** Questa API avrà il compito di estrarre e fornire l'elenco completo dei servizi che, a seguito dell'operazione di azzeramento, si trovano privi di un id\_tipo\_movimento. Questa lista costituirà l'input per la successiva fase di aggiornamento.  
* **API 2:** Questa API sarà responsabile dell'inserimento del tipo di movimento corretto all'interno di ciascun servizio individuato dall'API 1\. In questo modo, i servizi saranno allineati con il nuovo anno e pronti per essere elaborati correttamente da tutta la suite MasterCom.

L'adozione di questa strategia, basata sull'azzeramento dei riferimenti obsoleti e sull'utilizzo di API dedicate, assicura una transizione fluida e senza errori al nuovo anno, garantendo l'integrità dei dati e il corretto funzionamento degli accrediti e dei saldi di ogni anno.  
In pratica ogni anno conterrà solo ed esclusivamente l’id\_tipo\_movimento dell’anno a cui fa riferimento e che sarà differente per ogni anno in quanto MC2 non consente più di avere tipi movimento che possano essere su più anni.

Sarà inoltre necessario gestire il tipo "consolidamento mensa", poiché utilizza il campo "caratteristiche" per specificare il tipo di movimento da utilizzare per ciascun indirizzo. Se nella chiave è specificato "standard", significa che per tutti i consolidamenti mensa non specificati deve essere utilizzato l'ID tipo movimento standard.

Ad esempio: {"standard":402} all'interno delle caratteristiche per il servizio "CONSOLIDAMENTO\_MENSA" indica che per i consolidamenti mensa effettuati tramite MC2, deve essere utilizzato il tipo di movimento con ID 402 per tutti gli indirizzi.

# Struttura del DB e relative modifiche {#struttura-del-db-e-relative-modifiche}

## **Struttura DB** {#struttura-db}

L'attuale struttura del marketplace è così costituita:

 Table "public.marketplace"  
       Column        |       Type        |          Modifiers  
\---------------------+-------------------+--------------------------------------  
id\_marketplace      | bigint            | not null default nextval(('marketplace\_id\_seq'::text)::regclass)  
descrizione         | character varying | not null default ''::character varying  
tipo                | character varying | not null default ''::character varying  
chi\_inserisce       | bigint            | default '-1'::integer  
data\_inserimento    | bigint            | default 0  
tipo\_inserimento    | character varying | default ''::character varying  
chi\_modifica        | bigint            | default '-1'::integer  
data\_modifica       | bigint            | default 0  
tipo\_modifica       | character varying | default ''::character varying  
flag\_canc           | bigint            | default 0  
id\_tipo\_movimento   | integer           | default 0  
codice              | character varying | default ''::character varying  
movimento\_mc2       | character varying | default ''::character varying  
modulo\_origine      | character varying | default ''::character varying  
caratteristiche     | text              | default ''::text  
nome\_sitoapp        | character varying | default ''::character varying  
descrizione\_sitoapp | text              | default ''::text  
ordinamento         | character varying | default '000'::character varying  
validita\_inizio     | bigint            | default 0  
validita\_fine       | bigint            | default 0  
pubblica\_sitoapp    | character varying | default 'NO'::character varying  
categoria           | character varying | default 'STANDARD'::character varying

                                   Table "public.marketplace\_studenti\_acquisti"  
      Column      |       Type        |                                 Modifiers  
\------------------+-------------------+-----------------------------------------------------------------------    
id\_acquisto      | bigint            | not null default nextval(('marketplace\_acquisti\_id\_seq'::text)::regclass)  
id\_studente      | bigint            | not null default '-1'::integer  
id\_marketplace   | bigint            | not null default '-1'::integer  
contabilizzato   | character varying | default 'NO'::character varying  
chi\_inserisce    | bigint            | default '-1'::integer  
data\_inserimento | bigint            | default 0  
tipo\_inserimento | character varying | default ''::character varying  
chi\_modifica     | bigint            | default '-1'::integer  
data\_modifica    | bigint            | default 0  
tipo\_modifica    | character varying | default ''::character varying  
flag\_canc        | bigint            | default 0  
validita\_inizio  | bigint            | default '-1'::integer  
validita\_fine    | bigint            | default '-1'::integer  
opzioni          | text              | default ''::text
stato_ordine     | character varying | default ''::character varying

Categoria della tabella marketplace contiene il tipo di oggetto che si sta trattando, ad oggi è utilizzata solo la categoria STANDARD, in realtà andranno implementate diverse categorie quali ad esempio NEGOZIO, EXTRATIME, CONSOLIDAMENTO\_MENSA, SERVIZI\_GIORNALIERI (EXTRATIME è il servizio speciale realizzato per Myschool)

## **Query SQL x MasterCom** {#query-sql-x-mastercom}

Sarà da lanciare la seguente query

```
BEGIN;

ALTER TABLE public.marketplace_studenti_acquisti
ADD COLUMN stato_ordine character varying DEFAULT '' CHECK (
    stato_ordine IN ('', 'CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO')
);

UPDATE public.marketplace_studenti_acquisti
SET stato_ordine = '';

COMMIT;
```

### **Scopo:**

La query esegue le seguenti operazioni:

* Aggiunge una nuova colonna chiamata \`stato\_ordine\` alla tabella \`marketplace\_studenti\_acquisti\`. Questa colonna serve per tracciare lo stato di un ordine (ordinato, confermato, evaso).  
* Imposta un vincolo sulla colonna \`stato\_ordine\` per assicurarsi che possa contenere solo determinati valori predefiniti.  
* Inizializza il valore della colonna \`stato\_ordine\` a una stringa vuota per tutte le righe esistenti nella tabella.

Lo scopo principale di questa query è quello di preparare la tabella \`marketplace\_studenti\_acquisti\` per gestire lo stato degli ordini nel contesto del nuovo sistema di negozio online descritto nel documento. La colonna \`stato\_ordine\` permetterà di tracciare il progresso degli ordini, dalla loro creazione alla loro evasione, e il vincolo assicura che vengano utilizzati solo valori validi per lo stato. La transazione garantisce che tutte queste modifiche vengano eseguite in modo atomico e coerente.

[image1]: <data:image/png;base64,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>

[image2]: <data:image/png;base64,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>