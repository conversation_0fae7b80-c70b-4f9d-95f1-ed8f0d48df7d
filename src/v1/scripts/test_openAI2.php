<?php
require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres 
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres 
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once "../models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once "../models/class_masterAI.php"; // Implementa la classe MasterAI

// acquisisci utente e password dal terminale
$username = $argv[1];
$password = $argv[2];
$id_classe = $argv[3] ?? null;

if (empty($username) || empty($password) || empty($id_classe)) {
    die("Usage: php test_openAI2.php <username> <password> <id_classe>\n");
}

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if ($authorized) {
    // Ottieni il JWT token (valido per 30 giorni = 2592000 secondi)
    $jwt = $auth->getJWT('user', 2592000);
    echo "Login successful. JWT Token:\n" . $jwt . "\n";

    // Istanzia MasterAI
    $masterAI = new MasterAI($auth);

    // Parametri per la richiesta
    $input = [
        'data_inizio' => '', // opzionale
        'data_fine' => '',   // opzionale
        'db_richiesto' => '' // opzionale
    ];

    // Ottieni le statistiche della classe
    $statistiche = $masterAI->generaStatisticheVotiClasse($id_classe, $input);

    // Stampa il risultato
    echo "\nRisultato analisi classe $id_classe:\n";
    echo json_encode($statistiche, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "\n";

} else {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}
?>
