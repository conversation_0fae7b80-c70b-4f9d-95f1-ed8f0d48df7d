#!/usr/bin/php
<?php

require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom

require_once "../models/class_auth.php";


$username = 'nexus';
$password = '100modipernonENTRARE';

$data = new Data;
$auth   = new Auth($data);
$authorized = $auth->auth($username, $password);
if (true) {
	if ($authorized) {
        echo "Sincronizzazione anni utenti\n";
        $auth->data->syncYearsUsers();
        echo "Sincronizzazione terminata\n";
    } else {
        echo "Errore nella connessione al database\n";
    }
}