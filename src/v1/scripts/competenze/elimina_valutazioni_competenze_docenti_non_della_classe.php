#!/usr/bin/php
<?php

require_once "../models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once "../models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once "../models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once "../models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once "../models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom

require_once "../models/class_auth.php";
require_once "../models/class_student.php";
require_once "../models/class_competenze_studente.php";

// Funzione per mostrare l'help
function mostraHelp() {
	echo "Utilizzo: php elimina_valutazioni_competenze_docenti_non_della_classe.php [opzioni]\n";
	echo "Opzioni:\n";
	echo "  --classi=id1,id2,...     Elabora solo le classi specificate\n";
	echo "  --escludi=id1,id2,...    Esclude le classi specificate\n";
	echo "  --help                   Mostra questo messaggio di aiuto\n";
	exit(0);
}

// Parsing degli argomenti da linea di comando
$classi_da_elaborare = [];
$classi_da_escludere = [];

foreach ($argv as $arg) {
	if (strpos($arg, '--classi=') === 0) {
		$classi_str = substr($arg, 9);
		$classi_da_elaborare = explode(',', $classi_str);
		$classi_da_elaborare = array_map('intval', $classi_da_elaborare);
	} elseif (strpos($arg, '--escludi=') === 0) {
		$classi_str = substr($arg, 10);
		$classi_da_escludere = explode(',', $classi_str);
		$classi_da_escludere = array_map('intval', $classi_da_escludere);
	} elseif ($arg === '--help') {
		mostraHelp();
	}
}

$username = 'nexus';
$password = '100modipernonENTRARE';

$data = new Data;
$auth   = new Auth($data);
$authorized = $auth->auth($username, $password);
if (true) {
	if ($authorized) {
		$competenze_studente = new CompetenzeStudente($auth);
		$student = new Student($auth);

		// Ottieni l'anno scolastico attuale
		$anno_scolastico = $competenze_studente->getAnnoScolasticoAttuale();
		echo "Anno scolastico attuale: " . $anno_scolastico . "\n";

		// Ottieni tutti gli abbinamenti docente-classe
		// Esegui la query direttamente per ottenere gli abbinamenti
		$sql = "SELECT DISTINCT id_professore, id_classe FROM classi_prof_materie WHERE flag_canc = 0";
		$query = $auth->data->db->prepare($sql);
		$query->execute();
		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		// Crea una struttura dati per gli abbinamenti (solo docente-classe, ignora materia)
		$abbinamenti_mastercom = [];
		foreach ($rows as $row) {
			$abbinamenti_mastercom[$row['id_professore']][$row['id_classe']] = true;
		}
		echo "Abbinamenti docente-classe caricati: " . count($rows) . " abbinamenti\n";

		// Ottieni la mappatura tra ID CouchDB e ID Mastercom per i docenti
		echo "Creazione mappatura ID CouchDB -> ID Mastercom per i docenti...\n";

		// Ottieni tutti i docenti da CouchDB
		$query = [
			"selector" => [
				"type" => "user",
				"access_level" => ['$ne' => 'deleted'],
				"main_role" => ["type" => "professore"]
			],
			"limit" => 10000
		];

		$results = $auth->data->cdb->call('_find', 'POST', $query);

		// Crea la mappatura ID CouchDB -> ID Mastercom
		$mappa_id_couch_to_mastercom = [];
		$docenti_trovati = 0;

		if (isset($results['docs']) && count($results['docs']) > 0) {
			foreach ($results['docs'] as $docente) {
				if (isset($docente['_id']) && isset($docente['roles'][$auth->data->master_id]['professore'])) {
					$id_couch = $docente['_id'];
					$id_mastercom = $docente['roles'][$auth->data->master_id]['professore'][str_replace('/', '_', $anno_scolastico)];
					if ($id_mastercom > 0) {
						$mappa_id_couch_to_mastercom[$id_couch] = $id_mastercom;
						$docenti_trovati++;
					}
				}
			}
		}

		echo "Trovati $docenti_trovati docenti con mappatura ID CouchDB -> ID Mastercom\n";

		// Crea la struttura degli abbinamenti usando gli ID di CouchDB
		$abbinamenti = [];
		foreach ($mappa_id_couch_to_mastercom as $id_couch => $id_mastercom) {
			if (isset($abbinamenti_mastercom[$id_mastercom])) {
				$abbinamenti[$id_couch] = $abbinamenti_mastercom[$id_mastercom];
			}
		}

		echo "Creata mappatura degli abbinamenti con ID CouchDB: " . count($abbinamenti) . " docenti\n";

		// Costruisci la condizione WHERE per le classi
		$where_classi = "WHERE ordinamento = '0'";
		if (!empty($classi_da_elaborare)) {
			$where_classi .= " AND id_classe IN (" . implode(',', $classi_da_elaborare) . ")";
			echo "Elaborazione limitata alle classi: " . implode(', ', $classi_da_elaborare) . "\n";
		} elseif (!empty($classi_da_escludere)) {
			$where_classi .= " AND id_classe NOT IN (" . implode(',', $classi_da_escludere) . ")";
			echo "Escluse le classi: " . implode(', ', $classi_da_escludere) . "\n";
		}

		// Ottieni gli studenti in base ai filtri
		$sql = "SELECT
					id_studente,
					cognome,
					nome,
					id_classe
				FROM studenti_completi
				$where_classi
				ORDER BY cognome, nome";
		$query = $auth->data->db->prepare($sql);
		$query->execute();
		$elenco_studenti = $query->fetchAll(PDO::FETCH_ASSOC);

		echo "Numero totale di studenti da elaborare: " . count($elenco_studenti) . "\n";

		$valutazioni_eliminate = 0;
		$studenti_elaborati = 0;
		$studenti_con_valutazioni_eliminate = 0;

		// Cicla tutti gli studenti
		foreach ($elenco_studenti as $studente) {
			$id_studente = $studente['id_studente'];
			$id_classe = $studente['id_classe'];

			// Ottieni il documento delle competenze dello studente
			$competenze = $competenze_studente->getCompetenze($id_studente, $anno_scolastico);

			if (isset($competenze[$id_studente])) {
				$document = $competenze[$id_studente];
				$modificato = false;

				// Verifica se ci sono competenze da elaborare
				if (isset($document['competenze']) && count($document['competenze']) > 0) {
					$valutazioni_eliminate_studente = 0;

					// Elabora le competenze dello studente
					$risultato = elaboraCompetenzeStudente(
						$document['competenze'],
						$abbinamenti,
						$id_classe,
						$valutazioni_eliminate_studente,
						$mappa_id_couch_to_mastercom,
						$auth
					);

					// Se sono state eliminate valutazioni, aggiorna il documento
					if ($valutazioni_eliminate_studente > 0) {
						$competenze_studente->set($document['_id'], $document, $id_studente);
						$valutazioni_eliminate += $valutazioni_eliminate_studente;
						$studenti_con_valutazioni_eliminate++;
						echo "Studente ID: $id_studente - Eliminate $valutazioni_eliminate_studente valutazioni\n";
						$modificato = true;
					}
				}

				$studenti_elaborati++;
				if ($studenti_elaborati % 100 == 0) {
					echo "Elaborati $studenti_elaborati studenti...\n";
				}
			}
		}

		echo "\nElaborazione completata.\n";
		echo "Studenti elaborati: $studenti_elaborati\n";
		echo "Studenti con valutazioni eliminate: $studenti_con_valutazioni_eliminate\n";
		echo "Valutazioni eliminate totali: $valutazioni_eliminate\n";

	} else {
		print_r("Unauthorized\n");
	}
} else {
	print_r("scripts blocked\n");
}

/**
 * Ottiene informazioni su un docente a partire dal suo ID CouchDB
 *
 * @param string $id_couch ID CouchDB del docente
 * @param object $auth Oggetto di autenticazione
 * @return array|null Informazioni sul docente o null se non trovato
 */
function getInfoDocente($id_couch, $auth) {
	static $cache = [];

	// Verifica se l'informazione è già in cache
	if (isset($cache[$id_couch])) {
		return $cache[$id_couch];
	}

	// Ottieni il documento del docente da CouchDB
	$docente = $auth->data->cdb->call($id_couch, 'GET');

	if (isset($docente['_id'])) {
		$cache[$id_couch] = $docente;
		return $docente;
	}

	return null;
}

/**
 * Verifica se un docente è abbinato a una classe
 *
 * @param string $id_docente_couch ID CouchDB del docente
 * @param int $id_classe ID della classe
 * @param array $abbinamenti Mappa degli abbinamenti docente-classe
 * @param array $mappa_id_couch_to_mastercom Mappa da ID CouchDB a ID Mastercom
 * @param object $auth Oggetto di autenticazione
 * @return bool True se il docente è abbinato alla classe, False altrimenti
 */
function isDocenteDellaClasse($id_docente_couch, $id_classe, $abbinamenti, $mappa_id_couch_to_mastercom, $auth) {
	// Verifica diretta usando la mappa degli abbinamenti con ID CouchDB
	if (isset($abbinamenti[$id_docente_couch][$id_classe])) {
		return true;
	}

	// Se non troviamo una corrispondenza diretta, proviamo a ottenere l'ID Mastercom dal documento del docente
	if (!isset($mappa_id_couch_to_mastercom[$id_docente_couch])) {
		$docente = getInfoDocente($id_docente_couch, $auth);

		if ($docente && isset($docente['roles'][$auth->data->master_id]['professore'])) {
			$id_mastercom = $docente['roles'][$auth->data->master_id]['professore'];
			$mappa_id_couch_to_mastercom[$id_docente_couch] = $id_mastercom;

			// Verifica se l'ID Mastercom è abbinato alla classe
			$sql = "SELECT COUNT(*) as count FROM classi_prof_materie
					WHERE id_professore = :id_professore
					AND id_classe = :id_classe
					AND flag_canc = 0";
			$query = $auth->data->db->prepare($sql);
			$query->execute([':id_professore' => $id_mastercom, ':id_classe' => $id_classe]);
			$result = $query->fetch(PDO::FETCH_ASSOC);

			return ($result['count'] > 0);
		}
	} else {
		// Abbiamo l'ID Mastercom nella mappa, verifichiamo l'abbinamento
		$id_mastercom = $mappa_id_couch_to_mastercom[$id_docente_couch];

		$sql = "SELECT COUNT(*) as count FROM classi_prof_materie
				WHERE id_professore = :id_professore
				AND id_classe = :id_classe
				AND flag_canc = 0";
		$query = $auth->data->db->prepare($sql);
		$query->execute([':id_professore' => $id_mastercom, ':id_classe' => $id_classe]);
		$result = $query->fetch(PDO::FETCH_ASSOC);

		return ($result['count'] > 0);
	}

	return false;
}

/**
 * Elabora ricorsivamente le competenze di uno studente per eliminare le valutazioni
 * assegnate da docenti che non appartengono alla classe dello studente
 *
 * @param array &$elenco_competenze Array di competenze da elaborare
 * @param array $abbinamenti Abbinamenti docente-classe
 * @param int $id_classe ID della classe dello studente
 * @param int &$valutazioni_eliminate Contatore delle valutazioni eliminate
 * @param array $mappa_id_couch_to_mastercom Mappa da ID CouchDB a ID Mastercom
 * @param object $auth Oggetto di autenticazione
 * @return bool True se sono state apportate modifiche, False altrimenti
 */
function elaboraCompetenzeStudente(&$elenco_competenze, $abbinamenti, $id_classe, &$valutazioni_eliminate, $mappa_id_couch_to_mastercom = [], $auth = null) {
	$modificato = false;

	if (count($elenco_competenze) > 0) {
		foreach ($elenco_competenze as $key => $competenza) {
			// Verifica le valutazioni di questa competenza
			if (isset($competenza['valutazioni']) && count($competenza['valutazioni']) > 0) {
				foreach ($competenza['valutazioni'] as $key_val => $valutazione) {
					// Verifica se il docente che ha inserito la valutazione è della classe
					$id_docente_couch = $valutazione['insert_id'];

					// Verifica se esiste l'abbinamento docente-classe (ignora la materia)
					$docente_della_classe = isDocenteDellaClasse($id_docente_couch, $id_classe, $abbinamenti, $mappa_id_couch_to_mastercom, $auth);

					// Se il docente non è della classe, elimina la valutazione
					if (!$docente_della_classe) {
						unset($elenco_competenze[$key]['valutazioni'][$key_val]);
						$valutazioni_eliminate++;
						$modificato = true;
					}
				}

				// Riordina l'array delle valutazioni dopo le eliminazioni
				if (isset($elenco_competenze[$key]['valutazioni'])) {
					$elenco_competenze[$key]['valutazioni'] = array_values($elenco_competenze[$key]['valutazioni']);

					// Se non ci sono più valutazioni, elimina l'array
					if (count($elenco_competenze[$key]['valutazioni']) === 0) {
						unset($elenco_competenze[$key]['valutazioni']);
					}
				}
			}

			// Elabora ricorsivamente i figli
			if (isset($competenza['figli']) && count($competenza['figli']) > 0) {
				$modificato_figli = elaboraCompetenzeStudente($elenco_competenze[$key]['figli'], $abbinamenti, $id_classe, $valutazioni_eliminate, $mappa_id_couch_to_mastercom, $auth);
				if ($modificato_figli) {
					$modificato = true;
				}
			}
		}
	}

	return $modificato;
}

?>