<?php

$base_path = dirname(__FILE__) . "/../../../";

require_once $base_path . "models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once $base_path . "models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once $base_path . "models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once $base_path . "models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once $base_path . "models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once $base_path . "models/class_marketplace.php"; // Implementa la classe Marketplace

// Cambia la directory di lavoro per i percorsi relativi nelle classi
chdir($base_path);

// Acquisisci parametri dal terminale
$username = $argv[1];
$password = $argv[2];
$cleanup = $argv[3] ?? 'true'; // Parametro per eliminare i dati di test (default: true)
$db_richiesto = $argv[4] ?? ''; // Database opzionale

if (empty($username) || empty($password)) {
    die("Usage: php test_completo_marketplace.php <username> <password> [cleanup=true] [db_richiesto]\n" .
        "Esempio: php test_completo_marketplace.php admin password true\n" .
        "Parametri:\n" .
        "  cleanup: 'true' per eliminare i dati di test, 'false' per mantenerli\n" .
        "  db_richiesto: nome del database specifico (opzionale)\n");
}

$cleanup = ($cleanup === 'true' || $cleanup === '1');

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if (!$authorized) {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}

echo "Login successful.\n";
echo "=== TEST COMPLETO MARKETPLACE ===\n";
echo "Cleanup abilitato: " . ($cleanup ? 'SI' : 'NO') . "\n";
if (!empty($db_richiesto)) {
    echo "Database richiesto: {$db_richiesto}\n";
}
echo "\n";

// Istanzia Marketplace
$marketplace = new Marketplace($auth);

// Array per tenere traccia degli ID creati per il cleanup
$created_items = [
    'marketplace' => [],
    'acquisti' => []
];

// Contatori per i test
$test_totali = 0;
$test_successi = 0;
$test_fallimenti = 0;

function esegui_test($nome_test, $callback, &$test_totali, &$test_successi, &$test_fallimenti) {
    $test_totali++;
    echo "\n--- TEST: {$nome_test} ---\n";
    
    try {
        $risultato = $callback();
        if ($risultato) {
            echo "✓ SUCCESSO: {$nome_test}\n";
            $test_successi++;
            return true;
        } else {
            echo "✗ FALLIMENTO: {$nome_test}\n";
            $test_fallimenti++;
            return false;
        }
    } catch (Exception $e) {
        echo "✗ ERRORE: {$nome_test} - " . $e->getMessage() . "\n";
        $test_fallimenti++;
        return false;
    }
}

// TEST 1: Inserimento elemento marketplace standard
esegui_test("Inserimento elemento marketplace standard", function() use ($marketplace, $db_richiesto, &$created_items) {
    $dati = [
        'descrizione' => 'Test Servizio Mensa',
        'tipo' => 'MENSA',
        'categoria' => 'STANDARD',
        'ordinamento' => '001',
        'nome_sitoapp' => 'Servizio Mensa Test',
        'descrizione_sitoapp' => 'Servizio mensa per test automatici',
        'pubblica_sitoapp' => 'SI',
        'validita_inizio' => time(),
        'validita_fine' => time() + (365 * 24 * 60 * 60), // 1 anno
        'caratteristiche' => [
            'adesione_giornaliera' => 'SI',
            'orario_limite_ins' => '14:00',
            'orario_limite_del' => '08:00',
            'giornate_attive' => [
                'lun' => 'SI',
                'mar' => 'SI',
                'mer' => 'SI',
                'gio' => 'SI',
                'ven' => 'SI',
                'sab' => 'NO',
                'dom' => 'NO'
            ]
        ]
    ];
    
    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }
    
    $risultato = $marketplace->inserisciMarketplaceItem($dati);
    
    if (is_numeric($risultato) && $risultato > 0) {
        $created_items['marketplace'][] = $risultato;
        echo "  ID marketplace creato: {$risultato}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 2: Inserimento elemento marketplace negozio
esegui_test("Inserimento elemento marketplace negozio", function() use ($marketplace, $db_richiesto, &$created_items) {
    $dati = [
        'descrizione' => 'Test Prodotto Negozio',
        'tipo' => 'PRODOTTO',
        'categoria' => 'NEGOZIO',
        'ordinamento' => '002',
        'nome_sitoapp' => 'Prodotto Test',
        'descrizione_sitoapp' => 'Prodotto di test per negozio online',
        'pubblica_sitoapp' => 'SI',
        'validita_inizio' => time(),
        'validita_fine' => time() + (365 * 24 * 60 * 60), // 1 anno
        'caratteristiche' => [
            'oggetto_negozio' => [
                'valuta' => 'euro',
                'prezzo_unitario' => '15.50',
                'prezzo_scontato' => '12.90',
                'sconto_valido_dal' => date('d/m/Y'),
                'sconto_valido_al' => date('d/m/Y', time() + (30 * 24 * 60 * 60)),
                'inserire_movimento_immediatamente' => 'NO',
                'tipo_pagamento_disponibile' => 'online',
                'consenti_ordine_multiplo' => 'SI',
                'dettagli_ordine_multiplo' => [
                    'num_min' => '1',
                    'num_max' => '10',
                    'a_blocchi_di' => '1'
                ],
                'oggetto_disponibile_per' => 'tutti'
            ]
        ]
    ];
    
    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }
    
    $risultato = $marketplace->inserisciMarketplaceItem($dati);
    
    if (is_numeric($risultato) && $risultato > 0) {
        $created_items['marketplace'][] = $risultato;
        echo "  ID marketplace negozio creato: {$risultato}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 3: Recupero lista marketplace
esegui_test("Recupero lista marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
    $filter = [];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }
    
    $lista = $marketplace->getListaMarketplace($filter);
    
    if (is_array($lista) && count($lista) > 0) {
        echo "  Trovati " . count($lista) . " elementi marketplace\n";
        
        // Verifica che gli elementi creati siano presenti
        $trovati = 0;
        foreach ($created_items['marketplace'] as $id_creato) {
            if (isset($lista[$id_creato])) {
                $trovati++;
                echo "  ✓ Elemento {$id_creato} trovato: " . $lista[$id_creato]['descrizione'] . "\n";
            }
        }
        
        return $trovati === count($created_items['marketplace']);
    } else {
        echo "  Errore: " . json_encode($lista) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 4: Modifica elemento marketplace
esegui_test("Modifica elemento marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
    if (empty($created_items['marketplace'])) {
        echo "  Nessun elemento marketplace da modificare\n";
        return false;
    }

    $id_da_modificare = $created_items['marketplace'][0];

    // Prima recupero l'elemento esistente per avere tutti i campi
    $filter = ['id' => $id_da_modificare];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $lista_esistente = $marketplace->getListaMarketplace($filter);
    if (!isset($lista_esistente[$id_da_modificare])) {
        echo "  Errore: elemento da modificare non trovato\n";
        return false;
    }

    $elemento_esistente = $lista_esistente[$id_da_modificare];

    // Preparo tutti i campi come farebbe l'interfaccia, modificando solo alcuni valori
    $dati_modifica = [
        'descrizione' => 'Test Servizio Mensa MODIFICATO',
        'tipo' => $elemento_esistente['tipo'],
        'categoria' => $elemento_esistente['categoria'],
        'ordinamento' => $elemento_esistente['ordinamento'],
        'nome_sitoapp' => 'Servizio Mensa Test MODIFICATO',
        'descrizione_sitoapp' => $elemento_esistente['descrizione_sitoapp'] . ' - MODIFICATO',
        'pubblica_sitoapp' => $elemento_esistente['pubblica_sitoapp'],
        'validita_inizio' => $elemento_esistente['validita_inizio'],
        'validita_fine' => $elemento_esistente['validita_fine'],
        'caratteristiche' => $elemento_esistente['caratteristiche'] // Mantiene le caratteristiche esistenti
    ];

    if (!empty($db_richiesto)) {
        $dati_modifica['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->modificaMarketplaceItem($id_da_modificare, $dati_modifica);

    if (is_numeric($risultato) && $risultato == $id_da_modificare) {
        echo "  ✓ Elemento {$id_da_modificare} modificato con successo\n";
        echo "  - Descrizione aggiornata: " . $dati_modifica['descrizione'] . "\n";
        echo "  - Nome sitoapp aggiornato: " . $dati_modifica['nome_sitoapp'] . "\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// Prima di testare gli acquisti, dobbiamo trovare uno studente valido
$id_studente_test = null;

esegui_test("Ricerca studente per test acquisti", function() use ($data, &$id_studente_test) {
    // Cerca uno studente attivo nel database
    $sql = "SELECT id_studente FROM studenti WHERE flag_canc = 0 LIMIT 1";
    $query = $data->db->prepare($sql);
    $query->execute();
    $studenti = $query->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($studenti)) {
        $id_studente_test = $studenti[0]['id_studente'];
        echo "  Studente trovato per test: ID {$id_studente_test}\n";
        return true;
    } else {
        echo "  Nessuno studente trovato nel database\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 5: Inserimento acquisto marketplace (singolo studente)
esegui_test("Inserimento acquisto marketplace (singolo studente)", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (empty($created_items['marketplace']) || !$id_studente_test) {
        echo "  Prerequisiti mancanti (marketplace o studente)\n";
        return false;
    }

    $id_marketplace = $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'contabilizzato' => 'NO',
        'validita_inizio' => time(),
        'validita_fine' => time() + (30 * 24 * 60 * 60), // 30 giorni
        'opzioni' => [
            'note' => 'Test acquisto automatico',
            'preferenze' => ['vegetariano' => false]
        ]
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciMarketplaceAcquisto($dati);

    if (is_array($risultato) && isset($risultato['acquisti']) && !empty($risultato['acquisti'])) {
        $id_acquisto = $risultato['acquisti'][0];
        $created_items['acquisti'][] = $id_acquisto;
        echo "  ID acquisto creato: {$id_acquisto}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 6: Inserimento acquisto marketplace (multipli studenti)
esegui_test("Inserimento acquisto marketplace (multipli studenti)", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test, $data) {
    if (empty($created_items['marketplace']) || !$id_studente_test) {
        echo "  Prerequisiti mancanti (marketplace o studente)\n";
        return false;
    }

    // Cerca altri studenti per il test multiplo
    $sql = "SELECT id_studente FROM studenti WHERE flag_canc = 0 AND id_studente != :id_studente LIMIT 2";
    $query = $data->db->prepare($sql);
    $query->bindParam(':id_studente', $id_studente_test);
    $query->execute();
    $altri_studenti = $query->fetchAll(PDO::FETCH_ASSOC);

    $studenti_test = [$id_studente_test];
    foreach ($altri_studenti as $studente) {
        $studenti_test[] = $studente['id_studente'];
    }

    $id_marketplace = $created_items['marketplace'][1] ?? $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $studenti_test,
        'id_marketplace' => $id_marketplace,
        'contabilizzato' => 'NO',
        'stato_ordine' => 'CARRELLO',
        'validita_inizio' => time(),
        'validita_fine' => time() + (30 * 24 * 60 * 60), // 30 giorni
        'opzioni' => [
            'tipo_ordine' => 'multiplo',
            'quantita' => 2
        ]
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciMarketplaceAcquisto($dati);

    if (is_array($risultato) && isset($risultato['acquisti']) && !empty($risultato['acquisti'])) {
        foreach ($risultato['acquisti'] as $id_acquisto) {
            $created_items['acquisti'][] = $id_acquisto;
        }
        echo "  Acquisti multipli creati: " . count($risultato['acquisti']) . " per " . count($studenti_test) . " studenti\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 7: Recupero lista acquisti
esegui_test("Recupero lista acquisti", function() use ($marketplace, $db_richiesto, $created_items, $id_studente_test) {
    $filter = [
        'id_studente' => $id_studente_test
    ];

    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $lista = $marketplace->getListaMarketplaceAcquisti($filter);

    if (is_array($lista) && count($lista) > 0) {
        echo "  Trovati " . count($lista) . " acquisti per lo studente {$id_studente_test}\n";

        // Verifica che almeno alcuni degli acquisti creati siano presenti
        $trovati = 0;
        foreach ($created_items['acquisti'] as $id_creato) {
            if (isset($lista[$id_creato])) {
                $trovati++;
                echo "  ✓ Acquisto {$id_creato} trovato: " . $lista[$id_creato]['descrizione'] . "\n";
            }
        }

        return $trovati > 0;
    } else {
        echo "  Errore o nessun acquisto trovato: " . json_encode($lista) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 8: Test cambio stato acquisto - tutti gli stati
esegui_test("Test cambio stato acquisto - tutti gli stati", function() use ($marketplace, $db_richiesto, $created_items) {
    if (empty($created_items['acquisti'])) {
        echo "  Nessun acquisto da testare\n";
        return false;
    }

    $id_acquisto = $created_items['acquisti'][0];
    $stati_da_testare = ['CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO', ''];

    $successi = 0;
    foreach ($stati_da_testare as $stato) {
        $dati = [
            'id_acquisto' => $id_acquisto,
            'nuovo_stato' => $stato
        ];

        if (!empty($db_richiesto)) {
            $dati['db_richiesto'] = $db_richiesto;
        }

        $risultato = $marketplace->cambiaStatoAcquisto($dati);

        if (is_numeric($risultato) && $risultato == $id_acquisto) {
            echo "  ✓ Stato '{$stato}' impostato con successo\n";
            $successi++;
        } else {
            echo "  ✗ Errore impostando stato '{$stato}': " . json_encode($risultato) . "\n";
        }

        usleep(100000); // Pausa 0.1 secondi
    }

    return $successi === count($stati_da_testare);
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 9: Modifica acquisto marketplace
esegui_test("Modifica acquisto marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
    if (empty($created_items['acquisti'])) {
        echo "  Nessun acquisto da modificare\n";
        return false;
    }

    $id_acquisto = $created_items['acquisti'][0];
    $dati_modifica = [
        'id_acquisto' => $id_acquisto,
        'contabilizzato' => 'SI',
        'validita_fine' => time() + (60 * 24 * 60 * 60) // 60 giorni
    ];

    if (!empty($db_richiesto)) {
        $dati_modifica['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->modificaMarketplaceAcquisto($dati_modifica);

    if (is_numeric($risultato) && $risultato == $id_acquisto) {
        echo "  ✓ Acquisto {$id_acquisto} modificato con successo\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 10: Test gestione errori
esegui_test("Test gestione errori", function() use ($marketplace, $db_richiesto) {
    $test_errori = [
        // Test inserimento marketplace con dati mancanti
        [
            'funzione' => 'inserisciMarketplaceItem',
            'dati' => ['descrizione' => ''], // descrizione vuota
            'descrizione' => 'Inserimento marketplace con descrizione vuota'
        ],
        // Test cambio stato con ID inesistente
        [
            'funzione' => 'cambiaStatoAcquisto',
            'dati' => ['id_acquisto' => 999999, 'nuovo_stato' => 'ORDINATO'],
            'descrizione' => 'Cambio stato con ID acquisto inesistente'
        ],
        // Test cambio stato con stato non valido
        [
            'funzione' => 'cambiaStatoAcquisto',
            'dati' => ['id_acquisto' => 1, 'nuovo_stato' => 'STATO_INVALIDO'],
            'descrizione' => 'Cambio stato con stato non valido'
        ]
    ];

    $errori_gestiti = 0;
    foreach ($test_errori as $test) {
        if (!empty($db_richiesto)) {
            $test['dati']['db_richiesto'] = $db_richiesto;
        }

        $risultato = $marketplace->{$test['funzione']}($test['dati']);

        if (is_array($risultato) && !empty($risultato)) {
            echo "  ✓ Errore gestito: " . $test['descrizione'] . "\n";
            $errori_gestiti++;
        } else {
            echo "  ✗ Errore NON gestito: " . $test['descrizione'] . "\n";
        }
    }

    return $errori_gestiti === count($test_errori);
}, $test_totali, $test_successi, $test_fallimenti);

// CLEANUP: Eliminazione dati di test se richiesto
if ($cleanup) {
    echo "\n=== CLEANUP DATI DI TEST ===\n";

    // Elimina acquisti
    esegui_test("Cleanup acquisti", function() use ($marketplace, $db_richiesto, $created_items) {
        $eliminati = 0;
        foreach ($created_items['acquisti'] as $id_acquisto) {
            $dati = ['id_acquisto' => $id_acquisto];
            if (!empty($db_richiesto)) {
                $dati['db_richiesto'] = $db_richiesto;
            }

            $risultato = $marketplace->softDeleteItemAcquisti($dati);

            if (is_array($risultato) && $risultato['esito'] === 'OK') {
                echo "  ✓ Acquisto {$id_acquisto} eliminato\n";
                $eliminati++;
            } else {
                echo "  ✗ Errore eliminando acquisto {$id_acquisto}: " . json_encode($risultato) . "\n";
            }
        }

        return $eliminati === count($created_items['acquisti']);
    }, $test_totali, $test_successi, $test_fallimenti);

    // Elimina elementi marketplace
    esegui_test("Cleanup marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
        $eliminati = 0;
        foreach ($created_items['marketplace'] as $id_marketplace) {
            $dati = ['id_marketplace' => $id_marketplace];
            if (!empty($db_richiesto)) {
                $dati['db_richiesto'] = $db_richiesto;
            }

            $risultato = $marketplace->softDeleteItemMarketplace($dati);

            if (is_array($risultato) && $risultato['esito'] === 'OK') {
                echo "  ✓ Marketplace {$id_marketplace} eliminato\n";
                $eliminati++;
            } else {
                echo "  ✗ Errore eliminando marketplace {$id_marketplace}: " . json_encode($risultato) . "\n";
            }
        }

        return $eliminati === count($created_items['marketplace']);
    }, $test_totali, $test_successi, $test_fallimenti);
} else {
    echo "\n=== CLEANUP DISABILITATO ===\n";
    echo "Dati di test mantenuti nel database:\n";
    echo "- Marketplace creati: " . count($created_items['marketplace']) . " (IDs: " . implode(', ', $created_items['marketplace']) . ")\n";
    echo "- Acquisti creati: " . count($created_items['acquisti']) . " (IDs: " . implode(', ', $created_items['acquisti']) . ")\n";
}

// RIEPILOGO FINALE
echo "\n=== RIEPILOGO FINALE ===\n";
echo "Test totali eseguiti: {$test_totali}\n";
echo "Test riusciti: {$test_successi}\n";
echo "Test falliti: {$test_fallimenti}\n";

$percentuale_successo = $test_totali > 0 ? round(($test_successi / $test_totali) * 100, 2) : 0;
echo "Percentuale successo: {$percentuale_successo}%\n";

if ($test_fallimenti === 0) {
    echo "\n🎉 TUTTI I TEST SONO PASSATI! 🎉\n";
    echo "Le API del marketplace funzionano correttamente.\n";
} else {
    echo "\n⚠️  ALCUNI TEST SONO FALLITI ⚠️\n";
    echo "Verificare i messaggi di errore sopra riportati.\n";
}

// Dettagli sui test eseguiti
echo "\n=== DETTAGLI TEST ESEGUITI ===\n";
echo "1. Inserimento elemento marketplace standard\n";
echo "2. Inserimento elemento marketplace negozio\n";
echo "3. Recupero lista marketplace\n";
echo "4. Modifica elemento marketplace\n";
echo "5. Ricerca studente per test acquisti\n";
echo "6. Inserimento acquisto marketplace (singolo studente)\n";
echo "7. Inserimento acquisto marketplace (multipli studenti)\n";
echo "8. Recupero lista acquisti\n";
echo "9. Test cambio stato acquisto - tutti gli stati\n";
echo "10. Modifica acquisto marketplace\n";
echo "11. Test gestione errori\n";

if ($cleanup) {
    echo "12. Cleanup acquisti\n";
    echo "13. Cleanup marketplace\n";
}

echo "\n=== TEST COMPLETATO ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";

// Exit code basato sui risultati
exit($test_fallimenti > 0 ? 1 : 0);

?>
