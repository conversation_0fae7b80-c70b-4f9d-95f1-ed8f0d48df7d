# Test Automatici Marketplace

Questa cartella contiene i test automatici per le funzionalità del marketplace, incluse le caratteristiche del negozio e la gestione degli stati degli ordini.

## File di Test

### 1. `test_completo_marketplace.php`
Test completo di tutte le funzionalità del marketplace.

**Utilizzo:**
```bash
php test_completo_marketplace.php <username> <password> [cleanup=true] [db_richiesto]
```

**Parametri:**
- `username`: Nome utente per l'autenticazione
- `password`: Password per l'autenticazione  
- `cleanup`: `true` per eliminare i dati di test, `false` per mantenerli (default: true)
- `db_richiesto`: Nome del database specifico (opzionale)

**Esempio:**
```bash
php test_completo_marketplace.php admin mypassword true
php test_completo_marketplace.php admin mypassword false mastercom_2024_2025
```

**Test eseguiti:**
1. Inserimento elemento marketplace standard
2. Inserimento elemento marketplace negozio
3. Recupero lista marketplace
4. Modifica elemento marketplace
5. Ricerca studente per test acquisti
6. Inserimento acquisto marketplace (singolo studente)
7. Inserimento acquisto marketplace (multipli studenti)
8. Recupero lista acquisti
9. Test cambio stato acquisto - tutti gli stati
10. Modifica acquisto marketplace
11. Test gestione errori
12. Cleanup acquisti (se abilitato)
13. Cleanup marketplace (se abilitato)

### 2. `test_caratteristiche_negozio.php`
Test specifico per le caratteristiche del negozio e gli stati degli ordini.

**Utilizzo:**
```bash
php test_caratteristiche_negozio.php <username> <password> [cleanup=true] [db_richiesto]
```

**Parametri:** (stessi del test completo)

**Esempio:**
```bash
php test_caratteristiche_negozio.php admin mypassword true
```

**Test eseguiti:**
1. Prodotto negozio con pagamento immediato
2. Prodotto negozio con conferma manuale
3. Verifica caratteristiche salvate
4. Ricerca studente per test acquisti
5. Flusso completo stati ordine
6. Test annullamento ordine
7. Cleanup acquisti (se abilitato)
8. Cleanup marketplace (se abilitato)

**Caratteristiche testate:**
- ✓ Prezzi unitari e scontati
- ✓ Valuta e periodi di sconto
- ✓ Pagamento immediato vs conferma manuale
- ✓ Tipi di pagamento (online/predefinito)
- ✓ Ordini multipli e singoli
- ✓ Personalizzazioni prodotto
- ✓ Disponibilità per classi specifiche
- ✓ Stati ordine: CARRELLO → ORDINATO → CONFERMATO → EVASO
- ✓ Annullamento ordini

### 3. Test esistenti
- `test_cambio_stato_acquisto_marketplace.php`: Test per il cambio di stato di un singolo acquisto
- `test_completo_cambio_stato_acquisto_marketplace.php`: Test completo per tutti gli stati di un acquisto
- `test_verifica_tipi_movimento_marketplace.php`: Test per la verifica dei tipi movimento

## Funzionalità Testate

### API Marketplace
- `inserisciMarketplaceItem()`: Inserimento elementi marketplace
- `modificaMarketplaceItem()`: Modifica elementi marketplace
- `getListaMarketplace()`: Recupero lista marketplace
- `softDeleteItemMarketplace()`: Eliminazione logica elementi marketplace

### API Acquisti
- `inserisciMarketplaceAcquisto()`: Inserimento acquisti (singoli e multipli)
- `modificaMarketplaceAcquisto()`: Modifica acquisti
- `getListaMarketplaceAcquisti()`: Recupero lista acquisti
- `cambiaStatoAcquisto()`: Cambio stato ordini
- `softDeleteItemAcquisti()`: Eliminazione logica acquisti

### Stati Ordine Supportati
- `''` (vuoto): Stato iniziale
- `CARRELLO`: Prodotto nel carrello
- `ORDINATO`: Ordine effettuato, in attesa di conferma
- `CONFERMATO`: Ordine confermato dalla scuola
- `EVASO`: Ordine completato e consegnato
- `ANNULLATO`: Ordine annullato

### Caratteristiche Negozio Supportate
- Prezzi unitari e scontati con periodi di validità
- Valute personalizzabili
- Pagamento immediato o con conferma manuale
- Tipi di pagamento (online/predefinito)
- Ordini multipli con quantità min/max
- Personalizzazioni prodotto
- Disponibilità per classi/indirizzi specifici
- Immagini e gallerie prodotto

## Prerequisiti

1. **Database**: Deve esistere almeno uno studente attivo nel database
2. **Permessi**: L'utente deve avere permessi di amministratore o sadmin
3. **Tabelle**: Le tabelle `marketplace` e `marketplace_studenti_acquisti` devono avere i campi aggiornati:
   - `marketplace.caratteristiche` (text)
   - `marketplace_studenti_acquisti.stato_ordine` (varchar con CHECK constraint)
   - `marketplace_studenti_acquisti.opzioni` (text)

## Esecuzione dei Test

### Test Rapido
```bash
# Test completo con cleanup automatico
php test_completo_marketplace.php admin password

# Test caratteristiche negozio
php test_caratteristiche_negozio.php admin password
```

### Test con Dati Persistenti
```bash
# Mantiene i dati per ispezione manuale
php test_completo_marketplace.php admin password false

# Per eliminare successivamente i dati mantenuti, eseguire:
php test_completo_marketplace.php admin password true
```

### Test su Database Specifico
```bash
php test_completo_marketplace.php admin password true mastercom_2024_2025
```

## Interpretazione Risultati

### Output di Successo
```
🎉 TUTTI I TEST SONO PASSATI! 🎉
Le API del marketplace funzionano correttamente.
```

### Output di Fallimento
```
⚠️  ALCUNI TEST SONO FALLITI ⚠️
Verificare i messaggi di errore sopra riportati.
```

### Codici di Uscita
- `0`: Tutti i test sono passati
- `1`: Uno o più test sono falliti

## Risoluzione Problemi

### Errore "Nessuno studente trovato"
Assicurarsi che esista almeno uno studente attivo nel database:
```sql
SELECT COUNT(*) FROM studenti WHERE flag_canc = 0;
```

### Errore "Login failed"
Verificare username e password dell'utente amministratore.

### Errore "Column does not exist"
Eseguire le query SQL di aggiornamento delle tabelle presenti in `src/v1/configs/marketplace.sql`.

### Errore di permessi
L'utente deve avere ruolo di amministratore o sadmin per eseguire le operazioni di test.

## Note Tecniche

- I test utilizzano transazioni per garantire la consistenza dei dati
- Il cleanup elimina solo i dati creati durante il test
- I test sono progettati per essere idempotenti (possono essere eseguiti più volte)
- Ogni test è indipendente e può fallire senza compromettere gli altri
- I dati di test utilizzano timestamp correnti per evitare conflitti

## Manutenzione

Per aggiungere nuovi test:
1. Seguire la struttura esistente con la funzione `esegui_test()`
2. Aggiungere gli ID creati all'array `$created_items` per il cleanup
3. Gestire correttamente i parametri `db_richiesto`
4. Aggiornare questo README con i nuovi test
