<?php

$base_path = dirname(__FILE__) . "/../../../";

require_once $base_path . "models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once $base_path . "models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once $base_path . "models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once $base_path . "models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once $base_path . "models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once $base_path . "models/class_marketplace.php"; // Implementa la classe Marketplace

// Cambia la directory di lavoro per i percorsi relativi nelle classi
chdir($base_path);

// Acquisisci parametri dal terminale
$username = $argv[1];
$password = $argv[2];
$cleanup = $argv[3] ?? 'true'; // Parametro per eliminare i dati di test (default: true)
$db_richiesto = $argv[4] ?? ''; // Database opzionale

if (empty($username) || empty($password)) {
    die("Usage: php test_caratteristiche_negozio.php <username> <password> [cleanup=true] [db_richiesto]\n" .
        "Esempio: php test_caratteristiche_negozio.php admin password true\n" .
        "Questo script testa specificamente le caratteristiche del negozio e gli stati degli ordini.\n");
}

$cleanup = ($cleanup === 'true' || $cleanup === '1');

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if (!$authorized) {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}

echo "Login successful.\n";
echo "=== TEST CARATTERISTICHE NEGOZIO ===\n";
echo "Cleanup abilitato: " . ($cleanup ? 'SI' : 'NO') . "\n";
if (!empty($db_richiesto)) {
    echo "Database richiesto: {$db_richiesto}\n";
}
echo "\n";

// Istanzia Marketplace
$marketplace = new Marketplace($auth);

// Array per tenere traccia degli ID creati per il cleanup
$created_items = [
    'marketplace' => [],
    'acquisti' => []
];

// Contatori per i test
$test_totali = 0;
$test_successi = 0;
$test_fallimenti = 0;

function esegui_test($nome_test, $callback, &$test_totali, &$test_successi, &$test_fallimenti) {
    $test_totali++;
    echo "\n--- TEST: {$nome_test} ---\n";
    
    try {
        $risultato = $callback();
        if ($risultato) {
            echo "✓ SUCCESSO: {$nome_test}\n";
            $test_successi++;
            return true;
        } else {
            echo "✗ FALLIMENTO: {$nome_test}\n";
            $test_fallimenti++;
            return false;
        }
    } catch (Exception $e) {
        echo "✗ ERRORE: {$nome_test} - " . $e->getMessage() . "\n";
        $test_fallimenti++;
        return false;
    }
}

// TEST 1: Prodotto negozio con pagamento immediato
esegui_test("Prodotto negozio con pagamento immediato", function() use ($marketplace, $db_richiesto, &$created_items) {
    $dati = [
        'descrizione' => 'Maglietta Scuola',
        'tipo' => 'ABBIGLIAMENTO',
        'categoria' => 'NEGOZIO',
        'ordinamento' => '001',
        'nome_sitoapp' => 'Maglietta Ufficiale',
        'descrizione_sitoapp' => 'Maglietta ufficiale della scuola disponibile in varie taglie',
        'pubblica_sitoapp' => 'SI',
        'validita_inizio' => time(),
        'validita_fine' => time() + (365 * 24 * 60 * 60), // 1 anno
        'caratteristiche' => [
            'oggetto_negozio' => [
                'valuta' => 'euro',
                'prezzo_unitario' => '25.00',
                'prezzo_scontato' => '20.00',
                'sconto_valido_dal' => date('d/m/Y'),
                'sconto_valido_al' => date('d/m/Y', time() + (30 * 24 * 60 * 60)),
                'inserire_movimento_immediatamente' => 'SI',
                'tipo_pagamento_disponibile' => 'online',
                'consenti_ordine_multiplo' => 'SI',
                'dettagli_ordine_multiplo' => [
                    'num_min' => '1',
                    'num_max' => '5',
                    'a_blocchi_di' => '1'
                ],
                'caratteristica' => [
                    'taglie_disponibili' => ['XS', 'S', 'M', 'L', 'XL'],
                    'colori_disponibili' => ['Bianco', 'Blu', 'Rosso']
                ],
                'immagini' => [
                    'principale' => 'maglietta_principale.jpg',
                    'galleria' => ['maglietta_1.jpg', 'maglietta_2.jpg']
                ],
                'oggetto_disponibile_per' => 'tutti'
            ]
        ]
    ];
    
    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }
    
    $risultato = $marketplace->inserisciMarketplaceItem($dati);
    
    if (is_numeric($risultato) && $risultato > 0) {
        $created_items['marketplace'][] = $risultato;
        echo "  ID marketplace creato: {$risultato}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 2: Prodotto negozio con conferma manuale
esegui_test("Prodotto negozio con conferma manuale", function() use ($marketplace, $db_richiesto, &$created_items) {
    $dati = [
        'descrizione' => 'Libro Personalizzato',
        'tipo' => 'LIBRI',
        'categoria' => 'NEGOZIO',
        'ordinamento' => '002',
        'nome_sitoapp' => 'Libro Personalizzato',
        'descrizione_sitoapp' => 'Libro personalizzato con nome dello studente',
        'pubblica_sitoapp' => 'SI',
        'validita_inizio' => time(),
        'validita_fine' => time() + (365 * 24 * 60 * 60), // 1 anno
        'caratteristiche' => [
            'oggetto_negozio' => [
                'valuta' => 'euro',
                'prezzo_unitario' => '15.50',
                'inserire_movimento_immediatamente' => 'NO',
                'tipo_pagamento_disponibile' => 'predefinito',
                'consenti_ordine_multiplo' => 'NO',
                'caratteristica' => [
                    'personalizzazione_richiesta' => true,
                    'tempo_produzione_giorni' => 7
                ],
                'oggetto_disponibile_per' => 'solo_selezionati',
                'disponibile_per_classi' => [
                    'classe_1' => 'Classe 1A',
                    'classe_2' => 'Classe 2B'
                ]
            ]
        ]
    ];
    
    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }
    
    $risultato = $marketplace->inserisciMarketplaceItem($dati);
    
    if (is_numeric($risultato) && $risultato > 0) {
        $created_items['marketplace'][] = $risultato;
        echo "  ID marketplace creato: {$risultato}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 3: Verifica caratteristiche salvate
esegui_test("Verifica caratteristiche salvate", function() use ($marketplace, $db_richiesto, $created_items) {
    if (empty($created_items['marketplace'])) {
        echo "  Nessun elemento marketplace da verificare\n";
        return false;
    }

    $filter = ['id' => $created_items['marketplace'][0]];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }
    
    $lista = $marketplace->getListaMarketplace($filter);
    
    if (is_array($lista) && isset($lista[$created_items['marketplace'][0]])) {
        $elemento = $lista[$created_items['marketplace'][0]];
        $caratteristiche = $elemento['caratteristiche'];
        
        echo "  ✓ Caratteristiche recuperate correttamente\n";
        echo "  - Prezzo unitario: " . $caratteristiche['oggetto_negozio']['prezzo_unitario'] . "\n";
        echo "  - Prezzo scontato: " . $caratteristiche['oggetto_negozio']['prezzo_scontato'] . "\n";
        echo "  - Pagamento immediato: " . $caratteristiche['oggetto_negozio']['inserire_movimento_immediatamente'] . "\n";
        echo "  - Tipo pagamento: " . $caratteristiche['oggetto_negozio']['tipo_pagamento_disponibile'] . "\n";
        
        return isset($caratteristiche['oggetto_negozio']['prezzo_unitario']);
    } else {
        echo "  Errore nel recupero delle caratteristiche\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// Trova uno studente per i test degli acquisti
$id_studente_test = null;
esegui_test("Ricerca studente per test acquisti", function() use ($data, &$id_studente_test) {
    $sql = "SELECT id_studente FROM studenti WHERE flag_canc = 0 LIMIT 1";
    $query = $data->db->prepare($sql);
    $query->execute();
    $studenti = $query->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($studenti)) {
        $id_studente_test = $studenti[0]['id_studente'];
        echo "  Studente trovato per test: ID {$id_studente_test}\n";
        return true;
    } else {
        echo "  Nessuno studente trovato nel database\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 4: Flusso completo stati ordine
esegui_test("Flusso completo stati ordine", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (empty($created_items['marketplace']) || !$id_studente_test) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    // Crea un acquisto di test
    $id_marketplace = $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'contabilizzato' => 'NO',
        'stato_ordine' => 'CARRELLO',
        'validita_inizio' => time(),
        'validita_fine' => time() + (30 * 24 * 60 * 60),
        'opzioni' => ['taglia' => 'M', 'colore' => 'Blu']
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciMarketplaceAcquisto($dati);

    if (!is_array($risultato) || !isset($risultato['acquisti']) || empty($risultato['acquisti'])) {
        echo "  Errore creazione acquisto: " . json_encode($risultato) . "\n";
        return false;
    }

    $id_acquisto = $risultato['acquisti'][0];
    $created_items['acquisti'][] = $id_acquisto;
    echo "  ID acquisto creato: {$id_acquisto}\n";

    // Test del flusso completo degli stati
    $flusso_stati = ['CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO'];
    $successi = 0;

    foreach ($flusso_stati as $stato) {
        $dati_cambio = [
            'id_acquisto' => $id_acquisto,
            'nuovo_stato' => $stato
        ];

        if (!empty($db_richiesto)) {
            $dati_cambio['db_richiesto'] = $db_richiesto;
        }

        $risultato_cambio = $marketplace->cambiaStatoAcquisto($dati_cambio);

        if (is_numeric($risultato_cambio)) {
            echo "  ✓ Transizione a stato '{$stato}' completata\n";
            $successi++;
            usleep(200000); // Pausa 0.2 secondi
        } else {
            echo "  ✗ Errore transizione a '{$stato}': " . json_encode($risultato_cambio) . "\n";
        }
    }

    return $successi === count($flusso_stati);
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 5: Test annullamento ordine
esegui_test("Test annullamento ordine", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (empty($created_items['marketplace']) || !$id_studente_test) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    // Crea un altro acquisto per il test di annullamento
    $id_marketplace = $created_items['marketplace'][1] ?? $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'contabilizzato' => 'NO',
        'stato_ordine' => 'ORDINATO',
        'validita_inizio' => time(),
        'validita_fine' => time() + (30 * 24 * 60 * 60),
        'opzioni' => ['personalizzazione' => 'Test annullamento']
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciMarketplaceAcquisto($dati);

    if (!is_array($risultato) || !isset($risultato['acquisti']) || empty($risultato['acquisti'])) {
        echo "  Errore creazione acquisto: " . json_encode($risultato) . "\n";
        return false;
    }

    $id_acquisto = $risultato['acquisti'][0];
    $created_items['acquisti'][] = $id_acquisto;

    // Annulla l'ordine
    $risultato_annullamento = $marketplace->cambiaStatoAcquisto([
        'id_acquisto' => $id_acquisto,
        'nuovo_stato' => 'ANNULLATO',
        'db_richiesto' => $db_richiesto
    ]);

    if (is_numeric($risultato_annullamento)) {
        echo "  ✓ Ordine annullato con successo\n";
        return true;
    } else {
        echo "  ✗ Errore nell'annullamento: " . json_encode($risultato_annullamento) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// CLEANUP: Eliminazione dati di test se richiesto
if ($cleanup) {
    echo "\n=== CLEANUP DATI DI TEST ===\n";

    // Elimina acquisti
    if (!empty($created_items['acquisti'])) {
        esegui_test("Cleanup acquisti", function() use ($marketplace, $db_richiesto, $created_items) {
            $eliminati = 0;
            foreach ($created_items['acquisti'] as $id_acquisto) {
                $dati = ['id_acquisto' => $id_acquisto];
                if (!empty($db_richiesto)) {
                    $dati['db_richiesto'] = $db_richiesto;
                }

                $risultato = $marketplace->softDeleteItemAcquisti($dati);

                if (is_array($risultato) && $risultato['esito'] === 'OK') {
                    echo "  ✓ Acquisto {$id_acquisto} eliminato\n";
                    $eliminati++;
                } else {
                    echo "  ✗ Errore eliminando acquisto {$id_acquisto}: " . json_encode($risultato) . "\n";
                }
            }

            return $eliminati === count($created_items['acquisti']);
        }, $test_totali, $test_successi, $test_fallimenti);
    }

    // Elimina elementi marketplace
    if (!empty($created_items['marketplace'])) {
        esegui_test("Cleanup marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
            $eliminati = 0;
            foreach ($created_items['marketplace'] as $id_marketplace) {
                $dati = ['id_marketplace' => $id_marketplace];
                if (!empty($db_richiesto)) {
                    $dati['db_richiesto'] = $db_richiesto;
                }

                $risultato = $marketplace->softDeleteItemMarketplace($dati);

                if (is_array($risultato) && $risultato['esito'] === 'OK') {
                    echo "  ✓ Marketplace {$id_marketplace} eliminato\n";
                    $eliminati++;
                } else {
                    echo "  ✗ Errore eliminando marketplace {$id_marketplace}: " . json_encode($risultato) . "\n";
                }
            }

            return $eliminati === count($created_items['marketplace']);
        }, $test_totali, $test_successi, $test_fallimenti);
    }
} else {
    echo "\n=== CLEANUP DISABILITATO ===\n";
    echo "Dati di test mantenuti nel database:\n";
    echo "- Marketplace creati: " . count($created_items['marketplace']) . " (IDs: " . implode(', ', $created_items['marketplace']) . ")\n";
    echo "- Acquisti creati: " . count($created_items['acquisti']) . " (IDs: " . implode(', ', $created_items['acquisti']) . ")\n";
}

// RIEPILOGO FINALE
echo "\n=== RIEPILOGO FINALE ===\n";
echo "Test totali eseguiti: {$test_totali}\n";
echo "Test riusciti: {$test_successi}\n";
echo "Test falliti: {$test_fallimenti}\n";

$percentuale_successo = $test_totali > 0 ? round(($test_successi / $test_totali) * 100, 2) : 0;
echo "Percentuale successo: {$percentuale_successo}%\n";

if ($test_fallimenti === 0) {
    echo "\n🎉 TUTTI I TEST SONO PASSATI! 🎉\n";
    echo "Le caratteristiche del negozio e gli stati degli ordini funzionano correttamente.\n";
} else {
    echo "\n⚠️  ALCUNI TEST SONO FALLITI ⚠️\n";
    echo "Verificare i messaggi di errore sopra riportati.\n";
}

// Dettagli sui test eseguiti
echo "\n=== DETTAGLI TEST ESEGUITI ===\n";
echo "1. Prodotto negozio con pagamento immediato\n";
echo "2. Prodotto negozio con conferma manuale\n";
echo "3. Verifica caratteristiche salvate\n";
echo "4. Ricerca studente per test acquisti\n";
echo "5. Flusso completo stati ordine\n";
echo "6. Test annullamento ordine\n";

if ($cleanup) {
    echo "7. Cleanup acquisti\n";
    echo "8. Cleanup marketplace\n";
}

echo "\n=== CARATTERISTICHE TESTATE ===\n";
echo "✓ Prezzi unitari e scontati\n";
echo "✓ Valuta e periodi di sconto\n";
echo "✓ Pagamento immediato vs conferma manuale\n";
echo "✓ Tipi di pagamento (online/predefinito)\n";
echo "✓ Ordini multipli e singoli\n";
echo "✓ Personalizzazioni prodotto\n";
echo "✓ Disponibilità per classi specifiche\n";
echo "✓ Stati ordine: CARRELLO → ORDINATO → CONFERMATO → EVASO\n";
echo "✓ Annullamento ordini\n";

echo "\n=== TEST COMPLETATO ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";

// Exit code basato sui risultati
exit($test_fallimenti > 0 ? 1 : 0);

?>
