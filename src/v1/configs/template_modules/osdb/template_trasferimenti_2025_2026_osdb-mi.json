{"_id": "regtemplate_trasferimenti_2025_2026_osdb-mi", "nome": "DOMANDA COLLOQUIO TRASFERIMENTO 2025/2026", "versione": "1", "messaggio": "", "anno_scolastico": "2025_2026", "fasi": {"1": "<PERSON><PERSON> colloquio"}, "validita": {"1": {"modalita_inserimento": ["nuovo"], "inizio_validita": -1, "fine_validita": -1}, "2": {"inizio_validita": -1, "fine_validita": -1}}, "notifiche": {"1": {"email_segreteria": "si"}, "2": {"email_segreteria": "si"}}, "opzioni": {"stato": {"1": "m", "2": "d"}, "messaggio": "Selezionare un indirizzo", "numero_opzioni": "2", "consiglio": "o", "lista_opzioni": {"1_1": "SCUOLA SECONDARIA DI PRIMO GRADO - CLASSI PRIME", "1_2": "SCUOLA SECONDARIA DI PRIMO GRADO - CLASSI SECONDE", "1_3": "SCUOLA SECONDARIA DI PRIMO GRADO - CLASSI TERZE", "2_1": "LICEO SCIENTIFICO OPZIONE SCIENZE APPLICATE - CLASSI PRIME", "2_2": "LICEO SCIENTIFICO OPZIONE SCIENZE APPLICATE - CLASSI SECONDE", "2_3": "LICEO SCIENTIFICO OPZIONE SCIENZE APPLICATE - CLASSI TERZE", "2_4": "LICEO SCIENTIFICO OPZIONE SCIENZE APPLICATE - CLASSI QUARTE", "2_5": "LICEO SCIENTIFICO OPZIONE SCIENZE APPLICATE - CLASSI QUINTE", "3_1": "LICEO SCIENTIFICO TRADIZIONALE - CLASSI PRIME", "3_2": "LICEO SCIENTIFICO TRADIZIONALE - CLASSI SECONDE", "3_3": "LICEO SCIENTIFICO TRADIZIONALE - CLASSI TERZE", "3_4": "LICEO SCIENTIFICO TRADIZIONALE - CLASSI QUARTE", "3_5": "LICEO SCIENTIFICO TRADIZIONALE - CLASSI QUINTE", "4_1": "LICEO DELLE SCIENZE UMANE - CLASSI PRIME", "4_2": "LICEO DELLE SCIENZE UMANE - CLASSI SECONDE", "4_3": "LICEO DELLE SCIENZE UMANE - CLASSI TERZE", "4_4": "LICEO DELLE SCIENZE UMANE - CLASSI QUARTE", "4_5": "LICEO DELLE SCIENZE UMANE - CLASSI QUINTE", "5_1": "ISTITUTO TECNICO TECNOLOGICO (BIENNIO COMUNE) - CLASSI PRIME", "5_2": "ISTITUTO TECNICO TECNOLOGICO (BIENNIO COMUNE) - CLASSI SECONDE", "6_3": "ISTITUTO TECNICO TECNOLOGICO INFORMATICA - CLASSI TERZE", "6_4": "ISTITUTO TECNICO TECNOLOGICO INFORMATICA - CLASSI QUARTE", "6_5": "ISTITUTO TECNICO TECNOLOGICO INFORMATICA - CLASSI QUINTE", "7_3": "ISTITUTO TECNICO TECNOLOGICO ELETTRONICA - CLASSI TERZE", "7_4": "ISTITUTO TECNICO TECNOLOGICO ELETTRONICA - CLASSI QUARTE", "7_5": "ISTITUTO TECNICO TECNOLOGICO ELETTRONICA - CLASSI QUINTE", "8_3": "ISTITUTO TECNICO TECNOLOGICO ELETTROTECNICA - CLASSI TERZE", "8_4": "ISTITUTO TECNICO TECNOLOGICO ELETTROTECNICA - CLASSI QUARTE", "8_5": "ISTITUTO TECNICO TECNOLOGICO ELETTROTECNICA - CLASSI QUINTE", "9_3": "ISTITUTO TECNICO TECNOLOGICO MECCANICA E MECCATRONICA - CLASSI TERZE", "9_4": "ISTITUTO TECNICO TECNOLOGICO MECCANICA E MECCATRONICA - CLASSI QUARTE", "9_5": "ISTITUTO TECNICO TECNOLOGICO MECCANICA E MECCATRONICA - CLASSI QUINTE", "10_1": "ISTRUZIONE/FORMAZIONE PROFESSIONALE AUTOMOTIVE - CLASSI PRIME", "10_2": "ISTRUZIONE/FORMAZIONE PROFESSIONALE AUTOMOTIVE - CLASSI SECONDE", "11_1": "ISTRUZIONE/FORMAZIONE PROFESSIONALE MECCANICO INDUSTRIALE - CLASSI PRIME", "11_2": "ISTRUZIONE/FORMAZIONE PROFESSIONALE MECCANICO INDUSTRIALE - CLASSI SECONDE", "12_1": "ISTRUZIONE/FORMAZIONE PROFESSIONALE ELETTRICO - CLASSI PRIME", "12_2": "ISTRUZIONE/FORMAZIONE PROFESSIONALE ELETTRICO - CLASSI SECONDE", "13_1": "ISTRUZIONE/FORMAZIONE PROFESSIONALE TERMOIDRAULICO - CLASSI PRIME", "13_2": "ISTRUZIONE/FORMAZIONE PROFESSIONALE TERMOIDRAULICO - CLASSI SECONDE"}}, "studente": {"stato": {"1": "m", "2": "d"}, "messaggio": "", "campi": {"nominativo": {"1": "m", "2": "l"}, "residenza_domicilio": {"1": "m", "2": "m"}, "cittadinanza": {"1": "m", "2": "m"}, "contatti": {"1": "o", "2": "o"}, "scuola_provenienza": {"1": "m", "2": "l"}, "certificazioni_studente": {"1": "m", "2": "l"}, "altro_figlio": {"1": "m", "2": "m"}, "note": {"1": "o", "2": "o"}, "note_aggiuntive": {"1": "o", "2": "o"}, "note_direttore": "d", "voti_materie": "d", "presenze": "d", "appunti": "d"}}, "parenti": {"stato": {"1": "m", "2": "d"}, "messaggio": "", "campi": {"nominativo": {"1": "m", "2": "l"}, "residenza_domicilio": {"1": "m", "2": "m"}, "contatti": {"1": "m", "2": "m"}, "stato_civile": {"1": "o", "2": "o"}, "descrizione_professione": {"1": "o", "2": "o"}, "note_direttore": "d"}}, "consensi": {"stato": {"1": "m", "2": "l"}, "intestazione": false, "campi": {"privacy": {"stato": {"1": "m", "2": "l"}, "tipo": "consenso_unico", "messaggio": "", "header": "In merito alle “Categorie di dati trattati” - come avete letto nell’Informativa Privacy - si chiede di esprimere liberamente il consenso per il trattamento dei dati sanitari comunicati secondo le finalità di detta informativa.", "testo": "Consento al trattamento dei dati sanitari comunicati per le finalità descritte nell'informativa della privacy", "errore": "Occorre esprimere il consenso per poter inviare il modulo"}}, "piede": "<i><b>E' possibile scaricare le condizioni sulla privacy indicate ai seguenti collegamenti</b></i><br><div class='row m-20' ><div class='col-3'>REGOLAMENTO (UE) 2016/67</div><div class='col-6'> <a class='btn btn-primary' href='images/osdb/privacy_genitori_iscrizioni_mod_ii_18.03.2021.pdf' target='_blank'>Scarica</a></div></div>"}, "type": "registration_template", "status": "active"}