{"_id": "regtemplate_iscrizioni_2026_2027_preschool_educationest", "nome": "ISCRIZIONE 2026/2027 PRESCHOOL", "versione": "1", "messaggio": "<b>Valido per l'anno scolastico 2026/2027 e per tutti gli anni successivi previsti dal grado</b>", "anno_scolastico": "2026_2027", "fasi": {"1": "Preiscrizione", "2": "Iscrizione"}, "validita": {"1": {"modalita_inserimento": ["nuovo", "esistente"], "inizio_validita": -1, "fine_validita": -1}, "classi": [1000117, 1000116]}, "notifiche": {"1": {"email_segreteria": "si"}, "2": {"email_segreteria": "si"}}, "allegati": {"stato": {"1": "m", "2": "m"}, "intestazione": "Utiliz<PERSON><PERSON> i link relativi per scaricare il materiale desiderato.", "documenti": {"allegato_1": {"path": "images/educationest/", "file": "Privacy_2025_2026.pdf", "descrizione": "Privacy"}, "allegato_2": {"path": "images/educationest/", "file": "Condizioni_contratto_2026_2027_preschool.pdf", "descrizione": "Condizioni di contratto", "fase": "2"}, "allegato_3": {"path": "images/educationest/", "file": "Informativa_servizi_2026_2027_preschool.pdf", "descrizione": "Informativa servizi", "fase": "2"}}, "presa_visione": "m"}, "opzioni": {"stato": {"1": "m", "2": "l"}, "messaggio": "Selezionare la classe", "numero_opzioni": "1", "lista_opzioni": {"1": "PRESCHOOL 3/4 - VIA SGARZERIA", "2": "PRESCHOOL 4/5 - VIA SGARZERIA", "3": "PRESCHOOL 5/6 - VIA SGARZERIA", "4": "PRESCHOOL MISTA - VIA MONTE SABOTINO"}}, "studente": {"stato": {"1": "m", "2": "m"}, "messaggio": "", "campi": {"nominativo": {"1": "m", "2": "l"}, "citta_nascita_straniera": {"1": "o", "2": "l"}, "cittadinanza": {"1": "m", "2": "l"}, "residenza": {"1": "m", "2": "l"}, "contatti": {"1": "o", "2": "o"}, "scuola_provenienza_short": {"1": "m", "2": "l"}, "allergie_alimentari": {"1": "d", "2": "m"}, "allergie_alimentari_note": {"1": "d", "2": "o"}, "intolleranze_alimentari": {"1": "d", "2": "m"}, "intolleranze_alimentari_note": {"1": "d", "2": "o"}, "diete_speciali": {"1": "d", "2": "m"}, "diete_speciali_note": {"1": "d", "2": "o"}}}, "parenti": {"stato": {"1": "m", "2": "m"}, "messaggio": "", "campi": {"nominativo": {"1": "m", "2": "l"}, "citta_nascita_straniera": {"1": "o", "2": "l"}, "cittadinanza": {"1": "m", "2": "l"}, "residenza": {"1": "m", "2": "l"}, "contatti": {"1": "m", "2": "m"}, "telefono_lavoro": {"1": "o", "2": "o"}}}, "consensi": {"stato": {"1": "d", "2": "m"}, "intestazione": "", "campi": {"consenso_cgc": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_unico", "messaggio": "dichiarano di accettare le Condizioni Generali di Contratto come da documentazione allegata.", "header": "", "testo": "Autorizzazione", "errore": "Consenso necessario mancante"}, "consenso_b1": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_parente", "messaggio": "esprimono i seguenti consensi per la privacy in riferimento alla documentazione allegata:<br><br>lettera b1) \"l'espletamento di tutte le attività amministrative, organizzative, pedagogiche, didattiche, formative e valutative inerenti le attività del nostro istituto scolastico per dare esecuzione all'iscrizione alla scuola ed agli obblighi istituzionali che si instaurano nonché tutti gli adempimenti ad esso collegati (quali ad esempio quelli contabili, fiscali, di sicurezza sul lavoro, assicurativi\"", "errore": "Consenso necessario mancante"}, "consenso_b2": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_parente", "messaggio": "lettera b2) \"la realizzazione di foto riguardanti gli alunni, in occasione delle attività didattiche, ricreative, in pubbliche manifestazioni, di gite e visite di istruzione, di feste, la loro raccolta e pubblicazione, a titolo gratuito, anche ai sensi degli Artt. 96 e 97 della legge sul diritto d'autore n. 22.4.1941, n. 633, e degli Artt. 10 e 320 del Codice Civile, in brochure, pubblicazioni, nel sito internet e sui canali social della Scuola\"", "errore": "Consenso necessario mancante"}, "consenso_b3": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_parente", "messaggio": "lettera b3) \"la realizzazione di foto riguardanti i genitori, in occasione delle attività ricreative, in pubbliche manifestazioni, di gite e visite di istruzione, di feste, la loro raccolta e pubblicazione, a titolo gratuito, anche ai sensi degli Artt. 96 e 97 della legge sul diritto d'autore n. 22.4.1941, n. 633, e degli Artt. 10 e 320 del Codice Civile in brochure, pubblicazioni, nel sito internet e sui canali social della Scuola\"", "errore": "Consenso necessario mancante"}, "consenso_b4": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_parente", "messaggio": "lettera b4) \"l'invio periodico via e-mail ai genitori della classe di appartenenza dell'alunno, delle foto effettuate in occasione delle attività didattiche e ricreative, di gite e altre uscite didattiche, come allegati ai resoconti delle attività svolte dagli alunni denominate \"Newsletter Settimanali\"\"", "errore": "Consenso necessario mancante"}, "consenso_c": {"stato": {"1": "d", "2": "m"}, "tipo": "consenso_parente", "messaggio": "lettera c) \"il trattamento dei dati sanitari dell'alunno per diete, scelte di regimi alimentari speciali o intolleranze, al fine di dare seguito alle richieste presentate\"", "errore": "Consenso necessario mancante"}}}, "servizi": {"stato": {"1": "d", "2": "m"}, "intestazione": "", "campi": {"ingresso_anticipato": {"stato": {"1": "d", "2": "o"}, "tipo": "servizio_unico", "header": "chiedono l'iscrizione ai seguenti servizi:<br><br><b><PERSON><PERSON><PERSON> ingresso anticipato (tariffa flat mensile)</b>", "testo": "Ore 7:30-8:00 (€100/mese)"}, "note_ingresso_anticipato": {"stato": {"1": "d", "2": "o"}, "tipo": "note_servizio", "tipo_oggetto": "text", "header": "Note"}, "prolungamento_orario_mensile": {"stato": {"1": "d", "2": "o"}, "tipo": "servizio_unico", "header": "____________________________________________________________________________________________________<br><br><br><b>Servizio di prolungamento orario mensile (tariffa flat mensile)</b>", "testo": "Ore 16:00-17:30 (€100/mese)"}, "note_prolungamento_orario_mensile": {"stato": {"1": "d", "2": "o"}, "tipo": "note_servizio", "tipo_oggetto": "text", "header": "Note"}}}, "type": "registration_template", "status": "active"}