
CREATE SEQUENCE dati_autorizzazioni_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



CREATE TABLE dati_autorizzazioni (
	id_dato_autorizzazione integer NOT NULL DEFAULT nextval('dati_autorizzazioni_seq'),
	id_utente_riferimento bigint DEFAULT (-1),
	tipo_utente_riferimento character varying DEFAULT 'studente'::character varying,
	id_utente_autorizzante bigint DEFAULT (-1),
	tipo_utente_autorizzante character varying DEFAULT 'parente'::character varying,
	/* puo essere anche non valorizzata nel caso si parli per esempio di clausole pertinenti direttamente il genitore */
	ordinamento character varying DEFAULT ''::character varying,
	testo character varying DEFAULT ''::character varying,
	valore text default ''::text,
	scelta character varying DEFAULT ''::character varying,
	validita_inizio bigint DEFAULT (-1),
	validita_fine bigint DEFAULT (-1),
	/* in base al articolo  */
	chi_inserisce bigint DEFAULT (-1),
    data_inserimento bigint DEFAULT 0,
    tipo_inserimento character varying DEFAULT ''::character varying,
    chi_modifica bigint DEFAULT (-1),
    data_modifica bigint DEFAULT 0,
    tipo_modifica character varying DEFAULT ''::character varying,
    flag_canc bigint DEFAULT 0
);
/* tabella generica per registrare la specifica autorizzazione ad una certa clausola (x es.- privacy) fornita da un utente (e eventuale studente relativo)  */




CREATE SEQUENCE marketplace_filtri_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


CREATE TABLE marketplace_filtri (
	id_filtro integer NOT NULL DEFAULT nextval('marketplace_filtri_seq'),
	id_marketplace  bigint NOT NULL,
	/* ovviamente obbligatorio */

	id_scuola  bigint DEFAULT (-1),
	id_indirizzo bigint DEFAULT (-1),
	id_classe bigint DEFAULT (-1),
	id_studente bigint DEFAULT (-1),
	/* solitamente valorizzato solo uno dei 4, se sono tutti a -1 lo interpreato come un "tutti" */

	visibilita_inizio  bigint DEFAULT (-1),
	visibilita_fine  bigint DEFAULT (-1),
	/* inizio e fine della visibilità: uso i range temporali solo se > 0 */

	tipo character varying DEFAULT 'includi'::character varying,
	/* "includi" o "escludi" per indicare il tipo di riga-filtro */

	chi_inserisce bigint DEFAULT (-1),
    data_inserimento bigint DEFAULT 0,
    tipo_inserimento character varying DEFAULT ''::character varying,
    chi_modifica bigint DEFAULT (-1),
    data_modifica bigint DEFAULT 0,
    tipo_modifica character varying DEFAULT ''::character varying,
    flag_canc bigint DEFAULT 0
);
/* una riga di marketplace viene visulaizzata per uno studente in una data se è pubblica o se ritorna almeno una riga rispondente alla tabella precedente di tipo includi e nessuna riga di tipo escludi */



ALTER TABLE marketplace ADD COLUMN codice character varying DEFAULT '';
/* codice di raggruppamento degli articoli */

ALTER TABLE marketplace ADD COLUMN id_tipo_movimento integer DEFAULT (-1);
/* campo per effettuare automaticamente la movimentazione all'atto della sottoscrizione */

ALTER TABLE marketplace ADD COLUMN ripetibilita integer DEFAULT (-1);
/* quante volte può essere fruito nel range della visibilita: -1 infinito, 0-n numero fisso  */

ALTER TABLE marketplace ADD COLUMN tipo_validita character varying DEFAULT 'istantaneo':: character varying;
/* per ora "istantaneo" (nessuna durata temporale) e "anno_scolastico" (vale per tutto l'anno scolastico scelto al momento dell'acquisto) */

ALTER TABLE marketplace ADD COLUMN visibilita character varying DEFAULT 'pubblico':: character varying;
/* "pubblico", "privato", "filtrato". Nel terzo caso, vd. tab. marketplace_filtri */

ALTER TABLE marketplace ADD COLUMN caratteristiche text DEFAULT '';
/* campo per memorizzare le caratteristiche specifiche dell'articolo in formato JSON */


ALTER TABLE marketplace_studenti_acquisti ADD COLUMN validita_inizio bigint DEFAULT (-1);
ALTER TABLE marketplace_studenti_acquisti ADD COLUMN validita_fine bigint DEFAULT (-1);
/* il periodo temporale preciso, se > 0, in cui il servizio è valido per lo studente */

ALTER TABLE marketplace_studenti_acquisti ADD COLUMN stato_ordine character varying DEFAULT '' CHECK (
    stato_ordine IN ('', 'CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO')
);
/* campo per tracciare lo stato dell'ordine nel sistema negozio */

ALTER TABLE marketplace_studenti_acquisti ADD COLUMN opzioni text DEFAULT '';
/* campo per memorizzare le opzioni selezionate per l'acquisto in formato JSON */



CREATE SEQUENCE movimenti_acquisti_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


CREATE TABLE movimenti_acquisti (
	id_movimento_acquisto integer NOT NULL DEFAULT nextval('movimenti_acquisti_seq'),
	id_acquisto  bigint NOT NULL,
	id_movimento  bigint NOT NULL,

	chi_inserisce bigint DEFAULT (-1),
    data_inserimento bigint DEFAULT 0,
    tipo_inserimento character varying DEFAULT ''::character varying,
    chi_modifica bigint DEFAULT (-1),
    data_modifica bigint DEFAULT 0,
    tipo_modifica character varying DEFAULT ''::character varying,
    flag_canc bigint DEFAULT 0
);
/* Quando vengono generati il o i movimenti corrispondenti al tipo movimento su mc per un acquisto, registro qua i movimenti */

