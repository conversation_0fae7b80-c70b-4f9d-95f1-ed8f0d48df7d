<?php
namespace Aws\CloudWatchEvidently;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon CloudWatch Evidently** service.
 * @method \Aws\Result batchEvaluateFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchEvaluateFeatureAsync(array $args = [])
 * @method \Aws\Result createExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createExperimentAsync(array $args = [])
 * @method \Aws\Result createFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFeatureAsync(array $args = [])
 * @method \Aws\Result createLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLaunchAsync(array $args = [])
 * @method \Aws\Result createProject(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProjectAsync(array $args = [])
 * @method \Aws\Result deleteExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteExperimentAsync(array $args = [])
 * @method \Aws\Result deleteFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFeatureAsync(array $args = [])
 * @method \Aws\Result deleteLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLaunchAsync(array $args = [])
 * @method \Aws\Result deleteProject(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProjectAsync(array $args = [])
 * @method \Aws\Result evaluateFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise evaluateFeatureAsync(array $args = [])
 * @method \Aws\Result getExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getExperimentAsync(array $args = [])
 * @method \Aws\Result getExperimentResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getExperimentResultsAsync(array $args = [])
 * @method \Aws\Result getFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFeatureAsync(array $args = [])
 * @method \Aws\Result getLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLaunchAsync(array $args = [])
 * @method \Aws\Result getProject(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProjectAsync(array $args = [])
 * @method \Aws\Result listExperiments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExperimentsAsync(array $args = [])
 * @method \Aws\Result listFeatures(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFeaturesAsync(array $args = [])
 * @method \Aws\Result listLaunches(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLaunchesAsync(array $args = [])
 * @method \Aws\Result listProjects(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProjectsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putProjectEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putProjectEventsAsync(array $args = [])
 * @method \Aws\Result startExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startExperimentAsync(array $args = [])
 * @method \Aws\Result startLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startLaunchAsync(array $args = [])
 * @method \Aws\Result stopExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopExperimentAsync(array $args = [])
 * @method \Aws\Result stopLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopLaunchAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateExperiment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateExperimentAsync(array $args = [])
 * @method \Aws\Result updateFeature(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFeatureAsync(array $args = [])
 * @method \Aws\Result updateLaunch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLaunchAsync(array $args = [])
 * @method \Aws\Result updateProject(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProjectAsync(array $args = [])
 * @method \Aws\Result updateProjectDataDelivery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProjectDataDeliveryAsync(array $args = [])
 */
class CloudWatchEvidentlyClient extends AwsClient {}
