<?php

class Marketplace {
	private $user 		= null; // username

	/**
     * @param Data $Data oggetto connessione al database, da usare per effettuare l'autenticazione
     */
	public function __construct($user) {
		/*{{{ */
		// Inizializzo l' oggetto data
		$this->user = $user;
		/*}}}*/
	}


	/**
	 * Funzione privata
	 * serve a definire le strutture dei dati presenti in questa classe e a controllarne la correttezza prima di passarla alle
	 * funzioni che dovranno utilizzarla.
	 * La funzione esegue questo:
	 * 		- verifica che siano presenti e valorizzati correttamente i campi obbligatori
	 * 		- se un campo risulta presente ma non obbligatorio verifica che sia impostato correttamente, e nel caso lo valorizza.
	 * 		- se si tratta di un campo di cui deve essere fatto l'encoding per sicurezza, viene fatto
	 * 		- ripassa alla funzione che l'ha chiamata un array con all'interno tre strutture, la prima contente i campi con le loro definizioni
	 * 		  la seconda i dati rielaborati e la terza contenente gli eventuali errori riscontrati che bloccano l'esecuzione dell'inserimento del record
	*/
	private function definizioneCampi($tabella, $dati = "")
	{
		$elenco_campi = array();
		switch ($tabella) {
			case 'marketplace':
				// definizione elenco campi inseribili
				$elenco_campi['descrizione']['tipo'] 				= 'string';
				$elenco_campi['descrizione']['sicurezza'] 			= 'encode';
				$elenco_campi['descrizione']['obbligatorio'] 		= 'SI';
				$elenco_campi['nome_sitoapp']['tipo'] 				= 'string';
				$elenco_campi['nome_sitoapp']['sicurezza'] 			= 'encode';
				$elenco_campi['descrizione_sitoapp']['tipo'] 		= 'string';
				$elenco_campi['descrizione_sitoapp']['sicurezza'] 	= 'encode';
				$elenco_campi['pubblica_sitoapp']['tipo'] 			= 'string';
				$elenco_campi['pubblica_sitoapp']['sicurezza'] 		= 'encode';
				$elenco_campi['categoria']['tipo'] 					= 'string';
				$elenco_campi['categoria']['sicurezza'] 			= 'encode';
				$elenco_campi['ordinamento']['tipo'] 				= 'string';
				$elenco_campi['ordinamento']['sicurezza'] 			= 'encode';
				$elenco_campi['validita_inizio']['tipo']			= 'timestamp';
				$elenco_campi['validita_inizio']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_inizio']['default'] 		= 0;
				$elenco_campi['validita_inizio']['orario'] 			= '00:00:00';
				$elenco_campi['validita_fine']['tipo'] 				= 'timestamp';
				$elenco_campi['validita_fine']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_fine']['default'] 			= 0;
				$elenco_campi['validita_fine']['orario'] 			= '23:59:59';
				$elenco_campi['tipo']['tipo'] 						= 'string';
				$elenco_campi['tipo']['sicurezza'] 					= 'encode';
				$elenco_campi['tipo']['obbligatorio'] 				= 'SI';
				$elenco_campi['id_tipo_movimento']['tipo'] 			= 'id';
				$elenco_campi['codice']['tipo'] 					= 'string';
				$elenco_campi['caratteristiche']['tipo'] 			= 'dati_json';
				$elenco_campi['caratteristiche']['sicurezza'] 		= 'encode';
				$elenco_campi['acquisti_studenti']['tipo'] 			= 'integer'; //non della tabella marketplace, ma da count su marketplace_studenti_acquisti
				break;
			case 'marketplace_studenti_acquisti':
				// definizione elenco campi inseribili
				$elenco_campi['id_studente']['tipo'] 				= 'id';
				$elenco_campi['id_studente']['obbligatorio'] 		= 'SI';
				$elenco_campi['id_marketplace']['tipo'] 			= 'id';
				$elenco_campi['id_marketplace']['obbligatorio'] 	= 'SI';
				$elenco_campi['validita_inizio']['tipo']			= 'timestamp';
				$elenco_campi['validita_inizio']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_inizio']['default'] 		= 0;
				$elenco_campi['validita_inizio']['orario'] 			= '00:00:00';
				$elenco_campi['validita_fine']['tipo'] 				= 'timestamp';
				$elenco_campi['validita_fine']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_fine']['default'] 			= 0;
				$elenco_campi['validita_fine']['orario'] 			= '23:59:59';
				$elenco_campi['contabilizzato']['tipo'] 			= 'string';
				$elenco_campi['stato_ordine']['tipo'] 				= 'string';
				$elenco_campi['stato_ordine']['sicurezza'] 			= 'encode';
				$elenco_campi['opzioni']['tipo'] 					= 'dati_json';
				$elenco_campi['opzioni']['sicurezza'] 				= 'encode';
				break;
			case 'marketplace_studenti_acquisti_update':
				// definizione elenco campi inseribili
				$elenco_campi['id_acquisto']['tipo'] 				= 'id';
				$elenco_campi['id_acquisto']['obbligatorio'] 		= 'SI';
				$elenco_campi['validita_inizio']['tipo']			= 'timestamp';
				$elenco_campi['validita_inizio']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_inizio']['default'] 		= 0;
				$elenco_campi['validita_inizio']['orario'] 			= '00:00:00';
				$elenco_campi['validita_fine']['tipo'] 				= 'timestamp';
				$elenco_campi['validita_fine']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_fine']['default'] 			= 0;
				$elenco_campi['validita_fine']['orario'] 			= '23:59:59';
				$elenco_campi['contabilizzato']['tipo'] 			= 'string';
				$elenco_campi['stato_ordine']['tipo'] 				= 'string';
				$elenco_campi['stato_ordine']['sicurezza'] 			= 'encode';
				$elenco_campi['opzioni']['tipo'] 					= 'dati_json';
				$elenco_campi['opzioni']['sicurezza'] 				= 'encode';
				break;
			case 'misto':
				// definizione elenco campi inseribili
				$elenco_campi['descrizione']['tipo'] 				= 'string';
				$elenco_campi['descrizione']['sicurezza'] 			= 'encode';
				$elenco_campi['descrizione']['obbligatorio'] 		= 'SI';
				$elenco_campi['nome_sitoapp']['tipo'] 				= 'string';
				$elenco_campi['nome_sitoapp']['sicurezza'] 			= 'encode';
				$elenco_campi['descrizione_sitoapp']['tipo'] 		= 'string';
				$elenco_campi['descrizione_sitoapp']['sicurezza'] 	= 'encode';
				$elenco_campi['pubblica_sitoapp']['tipo'] 			= 'string';
				$elenco_campi['pubblica_sitoapp']['sicurezza'] 		= 'encode';
				$elenco_campi['categoria']['tipo'] 					= 'string';
				$elenco_campi['categoria']['sicurezza'] 			= 'encode';
				$elenco_campi['ordinamento']['tipo'] 				= 'string';
				$elenco_campi['ordinamento']['sicurezza'] 			= 'encode';
				$elenco_campi['validita_inizio']['tipo']			= 'timestamp';
				$elenco_campi['validita_inizio']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_inizio']['default'] 		= 0;
				$elenco_campi['validita_inizio']['orario'] 			= '00:00:00';
				$elenco_campi['validita_fine']['tipo'] 				= 'timestamp';
				$elenco_campi['validita_fine']['traduci']['tipo']	= 'data_base';
				$elenco_campi['validita_fine']['default'] 			= 0;
				$elenco_campi['validita_fine']['orario'] 			= '23:59:59';
				$elenco_campi['tipo']['tipo'] 						= 'string';
				$elenco_campi['tipo']['sicurezza'] 					= 'encode';
				$elenco_campi['tipo']['obbligatorio'] 				= 'SI';
				$elenco_campi['id_tipo_movimento']['tipo'] 			= 'id';
				$elenco_campi['codice']['tipo'] 					= 'string';
				$elenco_campi['caratteristiche']['tipo'] 			= 'dati_json';
				$elenco_campi['caratteristiche']['sicurezza'] 		= 'encode';
				// definizione elenco campi inseribili
				$elenco_campi['id_studente']['tipo'] 				= 'id';
				$elenco_campi['id_studente']['obbligatorio'] 		= 'SI';
				$elenco_campi['id_marketplace']['tipo'] 			= 'id';
				$elenco_campi['id_marketplace']['obbligatorio'] 	= 'SI';
				$elenco_campi['contabilizzato']['tipo'] 			= 'string';
				$elenco_campi['stato_ordine']['tipo'] 				= 'string';
				$elenco_campi['stato_ordine']['sicurezza'] 			= 'encode';
				$elenco_campi['opzioni']['tipo'] 					= 'dati_json';
				$elenco_campi['opzioni']['sicurezza'] 				= 'encode';
				break;
			}

		$errori = [];
		$campi_da_inserire = [];
		$stringa_valori = "";


		if(is_array($dati))	{
			//verifica campi preinserimento
			foreach ($elenco_campi as $key => $value) {
				if($value['obbligatorio'] == 'SI') {
					if (isset($dati[$key])) {
						switch ($value['tipo']) {
							case 'timestamp':
							case 'id':
								if(!(is_numeric($dati[$key]))) {
									$errori[] = "ATTENZIONE - il campo " . $key . " dovrebbe essere numerico ma risulta valorizzato male:". $dati[$key];
								}
								break;
							case 'string':
								if(strlen($dati[$key]) == 0) {
									$errori[] = "ATTENZIONE - il campo " . $key . " dovrebbe essere una stringa valorizzata ma risulta vuota";
								}
								break;
							case 'dati_json':
								if(strlen($dati[$key]) == 0) {
									$errori[] = "ATTENZIONE - il campo " . $key . " dovrebbe essere un json valorizzato ma risulta vuoto";
								}
								break;
						}
					}else{
						$errori[] = "ATTENZIONE - il campo " . $key . " obbligatorio non risulta valorizzato";
					}
				}else{
					switch ($value['tipo']) {
						case 'timestamp':
							if(!(is_numeric($dati[$key]))) {
								$dati[$key] = $value['default'];
							}else{
								$mat_orario = explode(':', $value['orario']);
								$dati[$key] = mktime($mat_orario[0],$mat_orario[1],$mat_orario[2],date('m', $dati[$key]),date('d', $dati[$key]),date('Y', $dati[$key]));
							}
							break;
						case 'id':
							if(!(is_numeric($dati[$key]))) {
								$dati[$key] = 0;
							}
							break;
						case 'string':
						case 'dati_json':
							break;
					}
				}
				if($value['sicurezza'] == 'encode')	{
					switch ($value['tipo']) {
						case 'string':
							$dati[$key] = $this->user->data->EncodeField($dati[$key]);
							break;
						case 'dati_json':
							$dati[$key] = json_encode($dati[$key]);
							break;
					}
				}
			}

			if($dati['id_marketplace'] > 0){
				$filter['id'] = $dati['id_marketplace'];
				$dati_elemento_marketplace = $this->getListaMarketplace($filter);
				if($dati['validita_inizio'] < $dati_elemento_marketplace[$dati['id_marketplace']]['validita_inizio']){
					$dati['validita_inizio'] = $dati_elemento_marketplace[$dati['id_marketplace']]['validita_inizio'];
				}
				if($dati['validita_fine'] > $dati_elemento_marketplace[$dati['id_marketplace']]['validita_fine']){
					$dati['validita_fine'] = $dati_elemento_marketplace[$dati['id_marketplace']]['validita_fine'];
				}
			}
			foreach ($dati as $nome => $valore){

					$campi_da_inserire['nomi'][] = $nome;


					switch ($elenco_campi[$nome]['tipo']) {
						case 'dati_json':
						case 'string':
							$stringa_valori .= "'".$valore."',";
							$campi_da_inserire['valori'][] = "'".$valore."'";
							break;
						default:
							$campi_da_inserire['valori'][] = $valore;
							$stringa_valori .= $valore.",";
							break;
				}
			}
			$stringa_valori = substr($stringa_valori, 0, -1);
		}


		$array_finale = [];
		$array_finale['struttura'] = $elenco_campi;
		$array_finale['dati'] = $dati;
		$array_finale['errori'] = $errori;
		$array_finale['query']['campi'] = $campi_da_inserire;
		$array_finale['query']['valori'] = $stringa_valori;

		return $array_finale;
	}


	/**
	 * Funzione privata
	 * serve a fornire dati tradotti da encoding vari sulla base della struttura definita in definizioneCampi
	*/
	private function traduzioneCampi($tabella, $dati)
	{
		$definizione_campi = $this->definizioneCampi($tabella);
		$elenco_campi = $definizione_campi['struttura'];

		if(is_array($dati))	{
			foreach ($elenco_campi as $key => $value) {
				if($value['sicurezza'] == 'encode')	{
					switch ($value['tipo']) {
						case 'string':
							$dati[$key] = $this->user->data->DecodeField($dati[$key]);
							break;
						case 'dati_json':
							$dati[$key] = json_decode($dati[$key], true);
							break;
					}
				}
				if(strlen($value['traduci']['tipo']) > 0)	{
					switch ($value['traduci']['tipo']) {
						case 'array':
							$dati[$key . '_tradotto'] = $value['traduci']['valori'][$dati[$key]];
							break;
						case 'data_base':
							$dati[$key . '_tradotto'] = date('d-m-Y', $dati[$key]);
							$dati[$key . '_iso_tradotto'] = date('Y-m-d', $dati[$key]);
							break;
					}
				}
			}
		}

		return $dati;
	}



	/**
	 * Funzione in GET
	 * recupera la lista degli oggetti di marketplace sulla base del filtro impostato $filter
	 * i valori possibili sono:
	 * $filter['descrizione'] deve essere una stringa di almeno 3 caratteri
	 * $filter['id'] deve contenere l'id da ricercare
	 * $filter['tipo'] deve contenere la descizione del tipo da ricercare, in caso di piÃ¹ tipi devono essere separati da virgola
	 * $filter['categoria'] deve contenere la descrizione della categoria da ricercare (STANDARD, NEGOZIO)
	 * $filter['id_studente'] deve contenere l'id_studente da utilizzare per estrarre i marketplace a lui abbinati
	 * $filter['db_richiesto'] deve contenere il nome completo del database su cui ricercare
     * @param array $filter
	 */
	public function getListaMarketplace($filter = null) {
		/*{{{ */

		if (strlen($filter['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($filter['db_richiesto']);
		}

		$array_query_filter = array();
		if ($filter['id'] > 0) {
			$array_query_filter[] = " id_marketplace=" . $filter['id'] . " ";
		}
		else
		{
			if (strlen($filter['descrizione']) >= 3) {
				$array_query_filter[] = " descrizione ilike '%" . $filter['descrizione'] . "%' ";
			}
			if (strlen($filter['tipo']) >= 3) {
				$array_query_filter_tipo = [];
				if(strpos($filter['tipo'],',') > 0){
                    $array_tipi = explode(',',$filter['tipo']);
					foreach($array_tipi as $singolo_tipo){
						$array_query_filter_tipo[] = " tipo ilike '%" . $singolo_tipo . "%' ";
					}
					$array_query_filter[] = ' ( ' . implode(' OR ', $array_query_filter_tipo) . ' ) ';

				}else{
					$array_query_filter[] = " tipo ilike '%" . $filter['tipo'] . "%' ";
				}
			}
			if (strlen($filter['categoria']) >= 3) {
				$array_query_filter[] = " categoria ilike '%" . $filter['categoria'] . "%' ";
			}
			if ($filter['id_studente'] > 0) {
				$array_query_filter[] = " id_marketplace IN (SELECT distinct id_marketplace FROM marketplace_studenti_acquisti where id_studente=" . $filter['id_studente'] . ") ";
			}
		}

		$query_filter = 'WHERE
							marketplace.flag_canc=0';

		if(count($array_query_filter) > 0) {
			$query_filter .= ' AND ' . implode(' AND ', $array_query_filter) . ' ';
		}


		$sql = "SELECT 	*,
					(SELECT COUNT(*) FROM marketplace_studenti_acquisti msa WHERE msa.id_marketplace = marketplace.id_marketplace AND msa.flag_canc = 0) AS acquisti_studenti
					FROM marketplace
					" . $query_filter . "
					ORDER BY
					ordinamento, descrizione, tipo";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$row_tradotta = $this->traduzioneCampi('marketplace', $row);
			$results[$row['id_marketplace']] = $row_tradotta;
		}

		if (strlen($filter['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
		/*}}}*/
	}


	/**
	 * Funzione in POST
	 * inserisce un nuovo elemento in marketplace sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['descrizione'] deve contenere la descrizione del servizio - varchar - obbligatorio
	 * $dati['db_richiesto'] deve contenere il nome completo del database su cui effettuare le modifiche
	 * $dati['tipo'] deve contenere il tipo del servizio (es. mensa) - varchar - obbligatorio
	 * $dati['id_tipo_movimento'] deve contenere l'id del tipo di movimentiÃ¬o di MC2 da abbinare all'articolo
	 * $dati['codice'] deve contenere il codice di collegamento al modulo delle preiscrizione per poterlo abbinare
	 * $dati['ordinamento'] deve contenere la stringa di tre carattteri per poterla ordinare, default 000
	 * $dati['nome_sitoapp'] deve contenere la stringa del nome da presentare sul sito genitori
	 * $dati['descrizione_sitoapp'] deve contenere la stringa per la decrizione da presentare sul sito genitori
	 * $dati['pubblica_sitoapp'] deve contenere il valore SI/NO per indicare se deve essere un servizio pubblicato o meno sul sito dei genitori
	 * $dati['categoria'] deve contenere il valore STANDARD/NEGOZIO per indicare se deve essere un servizio standard o se deve essere un servizio da presentare nella sezione negozio e quindi acquistabile
	 * $dati['validita_inizio'] deve contenere in timestamp la data di inizio validitÃ  del servizio
	 * $dati['validita_fine'] deve contenere in timestamp la data di fine validitÃ  del servizio
	 * $dati['caratteristiche'] deve contenere il json delle caratteristiche specifiche dell'articolo (es: ripetibilitÃ , frequenza (mensile, settimanale, ecc.))
	 * --esempio di dati (si precisa che le caratteristiche possono cambiare a seconda dell'articolo):
	 *
	 * 		{
	 *			"adesione_giornaliera": "SI",
	 *			"orario_limite_ins": "14:00",
	 *			"orario_limite_del": "08:00"
	 *		}
	 *
	 * --altro esempio di dati (si precisa che le caratteristiche possono cambiare a seconda dell'articolo):
	 *
	 * 		{
	 * 			"tipo": "EXTRA TIME",
	 * 			"descrizione": "Abbonamento A",
	 * 			"id_tipo_movimento": 69,
	 * 			"codice": "",
	 *    		"caratteristiche": {
	 *				        "periodicita": "settimanale",
	 *				        "num_max_opzioni_selezionabili": 3,
	 *				        "elenco_opzioni": {
	 *                                    "Lun": {
	 *                                            "descrizione" : "LunedÃ¬",
	 *                                            "weekday" : 1,
	 *                                            "elenco_opzioni": {
	 *                                                                "base": {
	 *                                                                          "descrizione" : "base",
	 *                                                                          "ora_inizio": "08:00",
	 *                                                                          "ora_fine": "14:00",
	 *                                                                          "costo_minuto_extratime": "5",
	 *                                                                          "arrotondamento_minuti": "10"
	 *                                                                        },
	 *                                                                "esteso": {
	 *                                                                          "descrizione" : "esteso",
	 *                                                                          "ora_inizio": "08:00",
	 *                                                                          "ora_fine": "18:00",
	 *                                                                          "costo_minuto_extratime": "10",
	 *                                                                          "arrotondamento_minuti": "10"
	 *                                                                          }
	 *                                                              }
	 *                                          },
	 *                                    "Sab": {
	 *                                            "descrizione" : "Sabato",
	 *                                            "weekday" : 6,
	 *                                            "elenco_opzioni": {
	 *                                                                "base": {
	 *                                                                          "descrizione" : "base",
	 *                                                                          "ora_inizio": "08:00",
	 *                                                                          "ora_fine": "14:00",
 	 *                                                                          "costo_minuto_extratime": "5",
	 *                                                                          "arrotondamento_minuti": "10"
	 *                                                                        },
	 *                                                                "esteso": {
	 *                                                                          "descrizione" : "esteso",
	 *                                                                          "ora_inizio": "08:00",
	 *                                                                          "ora_fine": "18:00",
 	 *                                                                          "costo_minuto_extratime": "10",
 	 *                                                                          "arrotondamento_minuti": "10"
	 *                                                                          }
	 *                                                              }
	 *                                          }
	 *                                  }
	 *                        }
	 * 		}
	 *
     * @param array $dati
	 */
	public function inserisciMarketplaceItem($dati_grezzi)
	{
		$nome_db = $dati_grezzi['db_richiesto'];
		unset($dati_grezzi['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}


		// definizione elenco campi inseribili, normalizzazione e preparazione dati query
		$definizione_campi = $this->definizioneCampi('marketplace', $dati_grezzi);

		$dati = $definizione_campi['dati'];
		$elenco_campi = $definizione_campi['struttura'];
		$errori = $definizione_campi['errori'];
		$campi_da_inserire = $definizione_campi['query']['campi'];
		$stringa_valori = $definizione_campi['query']['valori'];

		//settaggio current_user
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}


		// INSERIMENTO
		if(count($errori) == 0)
		{
			$sql = "INSERT INTO marketplace
				(" . implode(", ", $campi_da_inserire['nomi']) . ") VALUES (" . $stringa_valori . ") RETURNING id_marketplace";

			$query = $this->user->data->db->prepare($sql);
			$query->execute();
			$id_marketplace = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_marketplace'];

			$mat_oggetti = [
				"id_marketplace" => $id_marketplace
			];

			$this->user->data->inserisciLog($mat_oggetti, 'marketplace', $current_user, "NEXTAPI", "INSERIMENTO");

			$results = $id_marketplace;

			if (!isset($dati_grezzi['codice'])){
				// inserimento codice calcolato
				$codice = str_replace(' ', '_', $dati_grezzi['descrizione']);
				$codice = preg_replace('/[^A-Za-z0-9\-]/', '', $codice);
				$codice .= "_" . $id_marketplace;

				$sql = "UPDATE marketplace
					SET codice = '{$codice}'
					WHERE id_marketplace = {$id_marketplace}";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();
			}
		}else{

			$results = $errori;
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}

	public function modificaMarketplaceItem($id_marketplace, $dati_grezzi)
	{
		if ($id_marketplace > 0){
			$nome_db = $dati_grezzi['db_richiesto'];
			unset($dati_grezzi['db_richiesto']);
			if (strlen($nome_db) > 0) {
				$previous = $this->user->data->SetDb($nome_db);
			}

			// definizione elenco campi inseribili, normalizzazione e preparazione dati query
			$definizione_campi = $this->definizioneCampi('marketplace', $dati_grezzi);

			$dati = $definizione_campi['dati'];
			$elenco_campi = $definizione_campi['struttura'];
			$errori = $definizione_campi['errori'];
			$campi_da_inserire = $definizione_campi['query']['campi'];
			$stringa_valori = $definizione_campi['query']['valori'];

			//settaggio current_user
			$current_user = $this->user->isUserOrMc2User(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}

			// MODIFICA
			if(count($errori) == 0){
				$sql = "UPDATE marketplace SET ";
				foreach ($campi_da_inserire['nomi'] as $key => $nome) {
					if ($nome != 'id_marketplace') {
						if (!isset($dati_grezzi[$nome]) && $nome == 'caratteristiche') {
							continue;
						}
						$sql .= $nome . "=" . $campi_da_inserire['valori'][$key] . ",";
					}
				}

				$sql = substr($sql, 0, -1);
				$sql .= " WHERE id_marketplace=" . $id_marketplace;

				$query = $this->user->data->db->prepare($sql);
				$query->execute();

				$mat_oggetti = [
					"id_marketplace" => $id_marketplace
				];

				$this->user->data->inserisciLog($mat_oggetti, 'marketplace', $current_user, "NEXTAPI", "MODIFICA");

				$results = $id_marketplace;
			}else{
				$results = $errori;
			}

			if (strlen($nome_db) > 0) {
				$this->user->data->SetDb($previous);
			}
		} else {
			return false;
		}

		return $results;
	}

	/**
	 * Funzione in DELETE
	 * Elimina un articolo dal marketplace
	 * $dati['id_marketplace'] deve contenere l'id della riga da eliminare
     * @param array $dati
	 */
	public function softDeleteItemMarketplace($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		if ($dati['id_marketplace'] > 0){

			$id_marketplace = $dati['id_marketplace'];

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUserOrMc2User(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}

			if(count($errori) == 0){
				$delete = "UPDATE marketplace
							SET flag_canc = ".time()."
							WHERE id_marketplace = ".$id_marketplace. " RETURNING id_marketplace";
				$query = $this->user->data->db->prepare($delete);
				$query->execute();

				$id_marketplace_ritorno = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_marketplace'];
				if($id_marketplace_ritorno > 0){
					$mat_oggetti = [
						"id_marketplace" => $id_marketplace_ritorno
					];

					$this->user->data->inserisciLog($mat_oggetti, 'marketplace', $current_user, "NEXTAPI", "ELIMINAZIONE");
					$results['esito'] = 'OK';
				}else{
					$results['esito'] = 'ERRORE';
					$results['errori'] = "Nessun elemento da cancellare con l'id richiesto";
				}
			}else{
				$results['esito'] = 'ERRORE';
				$results['errori'] = $errori;
			}
		} else {
			$results['esito'] = 'ERRORE';
			$results['errori'] = "ID da cancellare non valorizzato correttamente";
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}


	/**
	 * Funzione in GET
	 * recupera la lista degli oggetti di marketplace sulla base del filtro impostato $filter
	 * i valori possibili sono:
	 * $filter['id'] deve contenere l'id del record da ricercare
	 * $filter['db_richiesto'] deve contenere il nome completo del database su cui ricercare
	 * $filter['id_classe'] deve contenere l'id della classe degli studenti da filtrare
	 * $filter['id_studente'] deve contenere l'id dello studente da filtrare
	 * $filter['id_marketplace'] deve contenere l'id del marketplace da filtrare
	 * $filter['contabilizzato'] deve contenere il valore che si intende filtrare (valori ammessi ['SI', 'NO'])
	 * $filter['validita_inizio']['start'] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno
	 * 		validita_inizio >= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)
	 * $filter['validita_inizio']['end'] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno
	 * 		validita_inizio <= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)
	 * $filter['validita_fine']['start'] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno
	 * 		validita_fine >= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)
	 * $filter['validita_fine']['end'] deve contenere il valore in timestamp per cui verranno cercate le righe che hanno
	 * 		validita_fine <= del valore passato (mettere 0 o '' o null se non si vuole impostare il filtro per questo valore)
	 *
	 * $filter['tipo_risultato'] se non precisato viene restituito l'array con chiave id_Acquisto di tutti gli acquisti trovati
	 *            se valorizzato a "id_studente" crea un array con chiave id_studente per ogni studente e un sottoarray per ogni acquisto
	 *            se valorizzato a "studente" crea un array con un contatore come chiave e un sottoarray per ogni acquisto
	 * $filter['data_adesione'] se valorizzato va a verificare se per quella data lo studente ha aderito al servizio
	 *            per ogni servizio che ha la caratteristica "adesione_giornaliera":"SI"
	 *
	 * @param array $filter
	 */
	public function getListaMarketplaceAcquisti($filter = null) {
		/*{{{ */

		if (strlen($filter['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($filter['db_richiesto']);
		}

		$array_query_filter = array();
		if ($filter['id'] > 0) {
			$array_query_filter[] = " id_acquisto=" . $filter['id'] . " ";
		}
		else
		{
			if(($filter['id_classe'] > 0)){
				$array_query_filter[] = " marketplace_studenti_acquisti.id_studente IN (
					SELECT id_studente FROM classi_studenti WHERE id_classe=" . $filter['id_classe'] . ") ";
			}
			if ($filter['id_studente'] > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.id_studente=" . $filter['id_studente'] . " ";
			}
			if ($filter['id_marketplace'] > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.id_marketplace=" . $filter['id_marketplace'] . " ";
			}
			if (strlen(trim($filter['contabilizzato'])) == 2) {
				$array_query_filter[] = " marketplace_studenti_acquisti.contabilizzato='" . $filter['contabilizzato'] . "' ";
			}
			if (intval($filter['validita_inizio']['start']) > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_inizio >= " . $filter['validita_inizio']['start'] . " ";
			}
			if (intval($filter['validita_inizio']['end']) > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_inizio <= " . $filter['validita_inizio']['end'] . " ";
			}
			if (intval($filter['validita_fine']['start']) > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_fine >= " . $filter['validita_fine']['start'] . " ";
			}
			if (intval($filter['validita_fine']['end']) > 0) {
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_fine <= " . $filter['validita_fine']['end'] . " ";
			}
			$mat_data_adesione = explode('-', $filter['data_adesione']);
			if(count($mat_data_adesione) == 3){
				$data_adesione_ts = mktime(0,0,0,$mat_data_adesione[1],$mat_data_adesione[2],$mat_data_adesione[0]);
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_inizio <= " . $data_adesione_ts . " ";
				$array_query_filter[] = " marketplace_studenti_acquisti.validita_fine >= " . $data_adesione_ts . " ";
			}
		}


		$query_filter = 'WHERE
							marketplace.id_marketplace=marketplace_studenti_acquisti.id_marketplace
							AND
							marketplace.flag_canc=0
							AND
							marketplace_studenti_acquisti.flag_canc=0';
		if(count($array_query_filter) > 0) {
			$query_filter .= ' AND ' . implode(' AND ', $array_query_filter) . ' ';
		}

		$sql = "SELECT 	marketplace_studenti_acquisti.*,
						marketplace.descrizione,
						marketplace.tipo,
						marketplace.id_tipo_movimento,
						marketplace.codice,
						marketplace.caratteristiche,
						marketplace.nome_sitoapp,
						marketplace.descrizione_sitoapp,
						marketplace.ordinamento,
						marketplace.validita_inizio as validita_inizio_marketplace,
						marketplace.validita_fine as validita_fine_marketplace,
						marketplace.pubblica_sitoapp,
						marketplace.categoria,
						studenti_join.cognome,
						studenti_join.nome,
						studenti_join.classe,
						studenti_join.sezione,
						studenti_join.descrizione_indirizzi,
						studenti_join.codice_indirizzi
					-- lasciare la LEFT JOIN vicino/subito dopo la tabella con la quale deve 'joinare', altrimenti da' errore
					FROM marketplace_studenti_acquisti
						LEFT JOIN (
							SELECT *
							FROM studenti_completi
							WHERE ordinamento = '0'
							ORDER BY preiscrizioni ASC
						) AS studenti_join ON studenti_join.id_studente = marketplace_studenti_acquisti.id_studente,
						-- vecchia left join
						--LEFT JOIN studenti_completi ON studenti_completi.id_studente = marketplace_studenti_acquisti.id_studente
							--AND studenti_completi.ordinamento = '0', --AND studenti_completi.preiscrizioni=0,
						marketplace
					" . $query_filter . "
					ORDER BY
					studenti_join.cognome, studenti_join.nome, marketplace_studenti_acquisti.id_studente, marketplace.ordinamento, marketplace.tipo, marketplace.descrizione";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		$array_dati = [];
		$data_adesione = '';
		$mat_data_adesione = explode('-', $filter['data_adesione']);
		if(count($mat_data_adesione) == 3){
			$data_adesione = $filter['data_adesione'];
			$giorno_settimana = date('N', mktime(0,0,0,$mat_data_adesione[1],$mat_data_adesione[2],$mat_data_adesione[0]));
		}else{
			$data_adesione = date('Y-m-d',$filter['data_adesione']);
			$giorno_settimana = date('N', $filter['data_adesione']);
		}

		if(strlen($data_adesione) > 0){
			$sql_adesione = "SELECT 	id_studente, id_marketplace, presenza
			FROM servizio_giornalieri_adesioni
			WHERE
				data_adesione='" . $data_adesione . "'
				AND
				flag_canc=0";

			$query_adesione = $this->user->data->db->prepare($sql_adesione);
			$query_adesione->execute();

			$rows_adesioni = $query_adesione->fetchAll(PDO::FETCH_ASSOC);
			$tot_adesioni = [];
			foreach ($rows_adesioni as $row_adesione) {
				$tot_adesioni[$row_adesione['id_studente']][$row_adesione['id_marketplace']]['presenza'] = $row_adesione['presenza'];
			}
		}

		foreach ($rows as $key => $row) {

			$row_tradotta = $this->traduzioneCampi('misto', $row);

			if($row_tradotta['caratteristiche']['adesione_giornaliera'] == 'SI'){
				switch ($giorno_settimana) {
					case '1':
						if($row_tradotta['caratteristiche']['giornate_attive']['lun'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '2':
						if($row_tradotta['caratteristiche']['giornate_attive']['mar'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '3':
						if($row_tradotta['caratteristiche']['giornate_attive']['mer'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '4':
						if($row_tradotta['caratteristiche']['giornate_attive']['gio'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '5':
						if($row_tradotta['caratteristiche']['giornate_attive']['ven'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '6':
						if($row_tradotta['caratteristiche']['giornate_attive']['sab'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					case '7':
						if($row_tradotta['caratteristiche']['giornate_attive']['dom'] == 'SI'){
							$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						}else{
							$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
						}
						break;
					default:
						$row_tradotta['adesione_giornaliera'] = 'ATTIVA';
						break;
				}
			}else{
				$row_tradotta['adesione_giornaliera'] = 'NON ATTIVA';
			}
			if(($row_tradotta['caratteristiche']['adesione_giornaliera'] == 'SI') && (strlen($data_adesione) > 0)){

				if(count($tot_adesioni[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]) == 1){
					$row_tradotta['adesione'] = 'SI';
					$row_tradotta['presenza'] = $tot_adesioni[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['presenza'];
					$row_tradotta['data_controllo_adesione'] = $filter['data_adesione'];

				}else{
					$row_tradotta['adesione'] = 'NO';
					$row_tradotta['data_controllo_adesione'] = $filter['data_adesione'];
				}
			}

			if(($filter['tipo_risultato'] == 'id_studente') || ($filter['tipo_risultato'] == 'studente')){
				$array_dati[$row['id_studente']][] = $row_tradotta;
			}else{
				$array_dati[$row['id_acquisto']] = $row_tradotta;
			}
		}
		$array_tmp = [];
		$cont = 0;
		if($filter['tipo_risultato'] == 'studente'){
			foreach ($array_dati as $id_studente => $row){
				$array_tmp[$cont] = $row;
				$cont++;
			}
			$array_dati = $array_tmp;
		}

		$results = $array_dati;
		if (strlen($filter['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
		/*}}}*/
	}


	/**
	 * Funzione in POST
	 * inserisce un nuovo elemanto in marketplace_studenti _acquisti sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['id_studente'] deve contenere l'id dello studente abbinato - obbligatorio
	 * $dati['id_marketplace'] deve contenere l'id del marketplace padre - obbligatorio
	 * $dati['contabilizzato'] deve contenere se giÃ  contabilizzato oppure no (valori: "SI", "NO") indica se Ã¨ giÃ  stato creato il movimento su MC2 per questo servizio abbinato allo studente
	 * $dati['validita_inizio'] deve contenere la data da cui il servizio Ã¨ attivo (se non presente o impostata a 0 si intende attivo dall'inizio dell'anno scolastico di appartenenza)
	 * $dati['validita_fine'] deve contenere la data fino a cui il servizio Ã¨ attivo (se non presente o impostata a 0 si intende attivo fino alla fine dell'anno scolastico di appartenenza)
	 * $dati['opzioni'] deve contenere l'array delle opzioni selezionate per quello studente nel servizio. Selezionate tra quelle disponibili nella definizione del marketplace
	 * --esempio di dati:
	 *
	 * {
	 * 		"id_studente": 1004680,
	 * 		"id_marketplace": 64,
	 * 		"contabilizzato": "SI",
	 * 		"validita_inizio": 1590233465,
	 * 		"validita_fine": 1590463465,
	 *    	"opzioni": 	{
	 *					"Lun": {
	 *                              "descrizione" : "LunedÃ¬",
	 *								"weekday" : 1,
	 *								"opzioni": {
	 *												"base": {
	 *															"descrizione" : "Base",
	 *															"ora_inizio": "08:00",
	 *															"ora_fine": "14:00",
 	 *                                                          "costo_minuto_extratime": "5",
	 *                                                          "arrotondamento_minuti": "10"
	 *														}
	 *											}
	 *							},
	 *					"Ven": {
	 *                              "descrizione" : "VenerdÃ¬",
	 *								"weekday" : 5,
	 *								"opzioni": {
	 *												"esteso": {
	 *															"descrizione" : "Esteso",
	 *															"ora_inizio": "08:00",
	 *															"ora_fine": "18:00",
 	 *                                                          "costo_minuto_extratime": "10",
	 *                                                          "arrotondamento_minuti": "10"
	 *														}
	 *											}
	 *							},
	 *    				}
	 * }
	 *
	 * @param array $dati
	 */
	public function inserisciMarketplaceAcquisto($dati_grezzi)
	{
		$nome_db = $dati_grezzi['db_richiesto'];
		unset($dati_grezzi['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		//gestisco il aso in cui invece che un array mi venga passato un solo id direttamente nella variabile
		if(!is_array($dati_grezzi['id_studente'])){
			$valore = $dati_grezzi['id_studente'];
			unset($dati_grezzi['id_studente']);
			$dati_grezzi['id_studente'][] = $valore;
		}
		unset($dati_lavoro['id_studente']);

		if(count($errori) == 0){
			foreach ($dati_grezzi['id_studente'] as $value) {
				$dati_lavoro = $dati_grezzi;
				unset($dati_lavoro['id_studente']);
				$dati_lavoro['id_studente'] = $value;

				// Imposto lo stato di default a '' se non specificato
				if (!isset($dati_lavoro['stato_ordine']) || empty($dati_lavoro['stato_ordine'])) {
					$dati_lavoro['stato_ordine'] = '';
				}

				// definizione elenco campi inseribili, normalizzazione e preparazione dati query
				$definizione_campi = $this->definizioneCampi('marketplace_studenti_acquisti', $dati_lavoro);

				$dati = $definizione_campi['dati'];
				$elenco_campi = $definizione_campi['struttura'];
				$errori = $definizione_campi['errori'];
				$campi_da_inserire = $definizione_campi['query']['campi'];
				$stringa_valori = $definizione_campi['query']['valori'];

				// INSERIMENTO
				if(count($errori) == 0)
				{
					$sql = "INSERT INTO marketplace_studenti_acquisti
						(" . implode(", ", $campi_da_inserire['nomi']) . ") VALUES (" . $stringa_valori . ") RETURNING id_acquisto";

					$query = $this->user->data->db->prepare($sql);
					$query->execute();
					$id_acquisto = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_acquisto'];

					$mat_oggetti = [
						"id_acquisto" => $id_acquisto
					];

					$this->user->data->inserisciLog($mat_oggetti, 'marketplace_studenti_acquisti', $current_user, "NEXTAPI", "INSERIMENTO");

					$results['acquisti'][] = $id_acquisto;
				}else{
					$results['errori'][] = $dati_lavoro;
				}

				if (strlen($nome_db) > 0) {
					$this->user->data->SetDb($previous);
				}
			}
		}else{
			$results['errori'] = $errori;
		}

		return $results;
	}


	/**
	 * Funzione per cambiare lo stato di un acquisto
	 * Cambia il campo stato_ordine di un acquisto nella tabella marketplace_studenti_acquisti
	 *
	 * @param array $dati_grezzi - deve contenere:
	 *   - id_acquisto: ID dell'acquisto da modificare (obbligatorio)
	 *   - nuovo_stato: nuovo stato da impostare (obbligatorio) - valori validi: '', 'CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO'
	 *   - db_richiesto: nome del database (opzionale)
	 *
	 * @return mixed - ID dell'acquisto se successo, array errori se fallimento
	 */
	public function cambiaStatoAcquisto($dati_grezzi)
	{
		$nome_db = $dati_grezzi['db_richiesto'];
		unset($dati_grezzi['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		$errori = [];
		$id_acquisto = $dati_grezzi['id_acquisto'];
		$nuovo_stato = $dati_grezzi['nuovo_stato'];

		// Validazione parametri obbligatori
		if (!($id_acquisto > 0)) {
			$errori['id_acquisto'] = 'ID acquisto obbligatorio e deve essere maggiore di 0';
		}

		if (!isset($nuovo_stato)) {
			$errori['nuovo_stato'] = 'Nuovo stato obbligatorio';
		}

		// Validazione stati validi
		$stati_validi = ['', 'CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO'];
		if (isset($nuovo_stato) && !in_array($nuovo_stato, $stati_validi)) {
			$errori['nuovo_stato'] = 'Stato non valido. Stati permessi: ' . implode(', ', $stati_validi);
		}

		// Settaggio current_user
		$current_user = $this->user->isUserOrMc2User(true);
		if (!($current_user > 0)) {
			$current_user = $this->user->isParent(true);
			if (!($current_user > 0)) {
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		// Verifica esistenza acquisto
		if (count($errori) == 0) {
			$filter = [];
			$filter['id'] = $id_acquisto;
			$dettaglio_acquisto = $this->getListaMarketplaceAcquisti($filter);

			if (empty($dettaglio_acquisto)) {
				$errori['acquisto_non_trovato'] = 'Acquisto non trovato';
			}
		}

		// Aggiornamento stato
		if (count($errori) == 0) {
			$sql = "UPDATE marketplace_studenti_acquisti
					SET stato_ordine = :nuovo_stato
					WHERE id_acquisto = :id_acquisto";

			$query = $this->user->data->db->prepare($sql);
			$query->bindParam(':nuovo_stato', $nuovo_stato);
			$query->bindParam(':id_acquisto', $id_acquisto);
			$query->execute();

			// Log dell'operazione
			$mat_oggetti = [
				"id_acquisto" => $id_acquisto,
				"nuovo_stato" => $nuovo_stato
			];

			$this->user->data->inserisciLog($mat_oggetti, 'marketplace_studenti_acquisti', $current_user, "NEXTAPI", "MODIFICA");

			$results = $id_acquisto;
		} else {
			$results = $errori;
		}

		if (strlen($nome_db) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return $results;
	}


	/**
	 * Funzione in PUT
	 * modifica un elemento in marketplace_studenti _acquisti sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['id_acquisto'] deve contenere l'id dell'acquisto che si vuole modificare - obbligatorio
	 * $dati['contabilizzato'] deve contenere se giÃ  contabilizzato oppure no (valori: "SI", "NO") indica se Ã¨ giÃ  stato creato il movimento su MC2 per questo servizio abbinato allo studente
	 * $dati['validita_inizio'] deve contenere la data da cui il servizio Ã¨ attivo (se non presente o impostata a 0 si intende attivo dall'inizio dell'anno scolastico di appartenenza)
	 * $dati['validita_fine'] deve contenere la data fino a cui il servizio Ã¨ attivo (se non presente o impostata a 0 si intende attivo fino alla fine dell'anno scolastico di appartenenza)
	 * ***** da estendere in futuro
	 * $dati['opzioni'] deve contenere l'array delle opzioni selezionate per quello studente nel servizio. Selezionate tra quelle disponibili nella definizione del marketplace
	 * --esempio di dati:
	 *
	 * {
	 * 		"id_acquisto": 1004680,
	 * 		"contabilizzato": "SI",
	 * 		"validita_inizio": 1593665543,
	 * 		"validita_fine": 1593680500,
	 *    	"opzioni": 	{
	 *					"Lun": {
	 *                              "descrizione" : "LunedÃ¬",
	 *								"weekday" : 1,
	 *								"opzioni": {
	 *												"base": {
	 *															"descrizione" : "Base",
	 *															"ora_inizio": "08:00",
	 *															"ora_fine": "14:00",
 	 *                                                          "costo_minuto_extratime": "5",
	 *                                                          "arrotondamento_minuti": "10"
	 *														}
	 *											}
	 *							},
	 *					"Ven": {
	 *                              "descrizione" : "VenerdÃ¬",
	 *								"weekday" : 5,
	 *								"opzioni": {
	 *												"esteso": {
	 *															"descrizione" : "Esteso",
	 *															"ora_inizio": "08:00",
	 *															"ora_fine": "18:00",
 	 *                                                          "costo_minuto_extratime": "10",
	 *                                                          "arrotondamento_minuti": "10"
	 *														}
	 *											}
	 *							},
	 *    				}
	 * }
	 *
	 * @param array $dati
	 */
	public function modificaMarketplaceAcquisto($dati_grezzi)
	{
		$nome_db = $dati_grezzi['db_richiesto'];
		unset($dati_grezzi['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		$id_acquisto = $dati_grezzi['id_acquisto'];

		if($id_acquisto > 0){
			$filter = [];
			$filter['id'] = $id_acquisto;
			$dettaglio_acquisto = $this->getListaMarketplaceAcquisti($filter);
			$dati_grezzi['id_marketplace'] = $dettaglio_acquisto[$id_acquisto]['id_marketplace'];
		}
		// definizione elenco campi inseribili, normalizzazione e preparazione dati query
		$definizione_campi = $this->definizioneCampi('marketplace_studenti_acquisti_update', $dati_grezzi);

		$errori = $definizione_campi['errori'];
		$campi_da_inserire = $definizione_campi['query']['campi'];

		//settaggio current_user
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		// AGGIORNAMENTO
		if((count($errori) == 0) and ($id_acquisto > 0))
		{
			$sql = "UPDATE marketplace_studenti_acquisti SET ";
			foreach ($campi_da_inserire['nomi'] as $key => $nome) {
				if($nome != 'id_acquisto'){
					//temporaneamente escludiamo opzioni
					if($nome != 'opzioni'){
						$sql .= $nome . "=" . $campi_da_inserire['valori'][$key] . ",";
					}
				}
			}

			$sql = substr($sql, 0, -1);
			$sql .= " WHERE id_acquisto=".$id_acquisto;

			$query = $this->user->data->db->prepare($sql);
			$query->execute();

			$mat_oggetti = [
				"id_acquisto" => $id_acquisto
			];

			$this->user->data->inserisciLog($mat_oggetti, 'marketplace_studenti_acquisti', $current_user, "NEXTAPI", "MODIFICA");

			$results = $id_acquisto;
		}else{
			$results = $errori;
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}



	/**
	 * Funzione in DELETE
	 * Elimina, settando flag_canc, un acquisto (un record) da marketplace_studenti_acquisti
	 * $dati['id_acquisto'] deve contenere l'id della riga da eliminare
     * @param array $dati
	 */
	public function softDeleteItemAcquisti($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		if ($dati['id_acquisto'] > 0){

			$id_acquisto = $dati['id_acquisto'];

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUserOrMc2User(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}

			if(count($errori) == 0){
				$delete = "UPDATE marketplace_studenti_acquisti
							SET flag_canc = ".time()."
							WHERE id_acquisto = ".$id_acquisto. " RETURNING id_acquisto";
				$query = $this->user->data->db->prepare($delete);
				$query->execute();

				$id_acquisto_ritorno = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_acquisto'];
				if($id_acquisto_ritorno > 0){
					$mat_oggetti = [
						"id_acquisto" => $id_acquisto_ritorno
					];

					$this->user->data->inserisciLog($mat_oggetti, 'marketplace_studenti_acquisti', $current_user, "NEXTAPI", "ELIMINAZIONE");
					$results['esito'] = 'OK';
				}else{
					$results['esito'] = 'ERRORE';
					$results['errori'] = "Nessun elemento da cancellare con l'id richiesto";
				}
			}else{
				$results['esito'] = 'ERRORE';
				$results['errori'] = $errori;
			}
		} else {
			$results['esito'] = 'ERRORE';
			$results['errori'] = "ID da cancellare non valorizzato correttamente";
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}

	/**
	 * Funzione in POST
	 * inserisce un nuovo elemanto in servizio_giornalieri_adesioni sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['data'] deve contenere la data dell'adesione in questione in formato timestamp, verrÃ  perÃ² salvata in formato ISO con separatore trattini es: 2021-01-23 - obbligatorio
	 * $dati['abbinamenti'][<id_studente> o <codice univoco tipo contatore>]['id_studente'] deve contenere l'id dello studente abbinato - obbligatorio
	 * $dati['abbinamenti'][<id_studente> o <codice univoco tipo contatore>]['id_marketplace'] deve contenere
	 *            l'id_marketplace a cui lo studente Ã¨ abbinato (se lo studente non risulta abbinato non verrÃ  inserito il record) - obbligatorio
	 * $dati['abbinamenti'][<id_studente> o <codice univoco tipo contatore>]['adesione'] valori possibili SI/NO serve per inserire quelle giuste ed eliminare quelle non sottoscritte- obbligatorio
	 * $dati['abbinamenti'][<id_studente> o <codice univoco tipo contatore>]['gruppo'] deve contenere eventualmente l'identificativo del gruppo a cui appartiene lo studente (varchar)
	 *
	 * @param array $dati
	 */
	public function inserisciAdesioneGiornalieraServizioStudente($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isParent(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}
		}

		if(count($errori) == 0){
			if((intval($dati['data']) > 0) || (count(explode('-', $dati['data'])) == 3)){
				if(count(explode('-', $dati['data'])) == 3){
					$data_adesione = trim($dati['data']);
				}else{
					$data_adesione = date('Y-m-d', intval($dati['data']));
				}
				$data_prenotazione = time();
				foreach ($dati['abbinamenti'] as $value) {
					$inserisci = true;
					$id_studente = intval($value['id_studente']);
					$id_marketplace = intval($value['id_marketplace']);
					$adesione = strtoupper($value['adesione']);
					if(strlen($value['gruppo']) > 0){
						$gruppo = $value['gruppo'];
					}else{
						$gruppo = '';
					}

					if(!($id_studente > 0)){
						$inserisci = false;
						$errori[] = "id_studente: " . $id_studente . " non valido";
					}

					if($id_marketplace > 0){
						$filter = [];
						$filter['id_studente'] = $id_studente;
						$marketplace_iscritti = $this->getListaMarketplaceAcquisti($filter);
						$trovato = false;
						foreach($marketplace_iscritti as $riga){
							if($riga['id_marketplace'] == $id_marketplace){
								$trovato = true;
							}
						}
						if(!$trovato){
							$inserisci = false;
							$errori[] = "studente con id: " . $id_studente . " non iscritto al marketplace: " . $id_marketplace;
						}
					}else{
						$inserisci = false;
						$errori[] = "id_marketplace: " . $id_marketplace . " non valido";
					}

					// INSERIMENTO
					if($inserisci)
					{
						$sql = "DELETE FROM servizio_giornalieri_adesioni
									WHERE
										id_studente = " . $id_studente . "
										AND
										id_marketplace = " . $id_marketplace . "
										AND
										data_adesione = '" . $data_adesione . "'";

						$query = $this->user->data->db->prepare($sql);
						$query->execute();
						$results['result'] = "OK";
						$results['messaggio'] = "Prenotazione eliminata";

						if($adesione == 'SI'){
							$sql = "INSERT INTO servizio_giornalieri_adesioni
								(id_studente, id_marketplace, data_adesione, gruppo, data_prenotazione) VALUES (" . $id_studente . ", " . $id_marketplace . ",'" . $data_adesione . "','" . $gruppo . "', " . $data_prenotazione . ")";

							$query = $this->user->data->db->prepare($sql);
							$query->execute();

							$mat_oggetti = [
								"id_studente" => $id_studente,
								"id_marketplace" => $id_marketplace,
								"data_adesione" => $data_adesione
							];

							$this->user->data->inserisciLog($mat_oggetti, 'servizio_giornalieri_adesioni', $current_user, "NEXTAPI", "INSERIMENTO");

							$results['adesioni'][] = $mat_oggetti;
							$results['result'] = "OK";
							$results['messaggio'] = "Prenotazione inserita";
						}
					}
					if (strlen($nome_db) > 0) {
						$this->user->data->SetDb($previous);
					}
				}
			}else{
				$errori[] = "data: " . $dati['data'] . " non corretta";
			}
		}
		if(count($errori) > 0){
			$results['errori'] = $errori;
		}

		return $results;
	}


	/**
	 * Funzione in DELETE
	 * Elimina, settando flag_canc, un acquisto (un record) da marketplace_studenti_acquisti
	 * $dati['id_studente'] deve contenere l'id studente della riga da eliminare (obbligatorio)
	 * $dati['id_marketplace'] deve contenere l'id marketplace della riga da eliminare (obbligatorio)
	 * $dati['data_adesione'] deve contenere la data dell'elemento della riga da eliminare (obbligatorio)
	 * $dati['db_richiesto'] l'eventuale nome del db su cui operare
     * @param array $dati
	 */
	public function softDeleteAdesioneGiornalieraServizioStudente($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		$data_esplosa = explode('-',$dati['data_adesione']);
		if (($dati['id_studente'] > 0) && ($dati['id_marketplace'] > 0) && (count($data_esplosa) == 3)){

			$id_studente = $dati['id_studente'];
			$id_marketplace = $dati['id_marketplace'];
			$data_adesione = $dati['data_adesione'];

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUserOrMc2User(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$current_user = $this->user->isParent(true);
					if(!($current_user > 0)){
						$errori['utente_non_valido'] = $this->user->getUserInfo();
					}
				}
			}

			if(count($errori) == 0){
				$delete = "UPDATE servizio_giornalieri_adesioni
							SET flag_canc = ".time()."
							WHERE id_studente = ".$id_studente. "
							AND
							id_marketplace = ".$id_marketplace. "
							AND
							data_adesione = '".$data_adesione. "'
							RETURNING id_studente";
				$query = $this->user->data->db->prepare($delete);
				$query->execute();

				$id_studente_ritorno = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_studente'];
				if($id_studente_ritorno > 0){
					$mat_oggetti = [
						"id_studente" => $id_studente_ritorno,
						"id_marketplace" => $id_marketplace,
						"data_adesione" => $data_adesione
					];

					$this->user->data->inserisciLog($mat_oggetti, 'servizio_giornalieri_adesioni', $current_user, "NEXTAPI", "ELIMINAZIONE");
					$results['esito'] = 'OK';
				}else{
					$results['esito'] = 'ERRORE';
					$results['errori'] = "Nessun elemento da cancellare con i dati forniti";
				}
			}else{
				$results['esito'] = 'ERRORE';
				$results['errori'] = $errori;
			}
		} else {
			$results['esito'] = 'ERRORE';
			$results['errori'] = "Dati forniti per la cancellazione non valorizzati correttamente";
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}

	/**
	 * Funzione in DELETE
	 * Elimina, settando flag_canc, un consolidamento per un tipo_movimento riferito ai servizi giornalieri
	 * $dati['id_consolidamento'] deve contenere l'id dell'elemento da eliminare (obbligatorio)
	 * $dati['db_richiesto'] l'eventuale nome del db su cui operare
     * @param array $dati
	 */
	public function softDeleteAdesioneGiornalieraConsolidamento($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		if ($dati['id_consolidamento'] > 0){

			$id_consolidamento = $dati['id_consolidamento'];

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUserOrMc2User(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}

			if(count($errori) == 0){
				$delete = "UPDATE servizi_giornalieri_consolidamento
							SET flag_canc = ".time()."
							WHERE id_consolidamento = ".$id_consolidamento. "
							RETURNING id_consolidamento";
				$query = $this->user->data->db->prepare($delete);
				$query->execute();

				$id_consolidamento_ritorno = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_consolidamento'];
				if($id_consolidamento_ritorno > 0){
					$mat_oggetti = [
						"id_consolidamento" => $id_consolidamento_ritorno
					];

					$this->user->data->inserisciLog($mat_oggetti, 'servizi_giornalieri_consolidamento', $current_user, "NEXTAPI", "ELIMINAZIONE");
					$results['esito'] = 'OK';
				}else{
					$results['esito'] = 'ERRORE';
					$results['errori'] = "Nessun elemento da cancellare con i dati forniti";
				}
			}else{
				$results['esito'] = 'ERRORE';
				$results['errori'] = $errori;
			}
		} else {
			$results['esito'] = 'ERRORE';
			$results['errori'] = "Dati forniti per la cancellazione non valorizzati correttamente";
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
	}


	/**
	 * Funzione in GET
	 * recupera la lista degli delle prenotazioni giornaliere di uno studente
	 * i valori possibili sono:
	 * $filter['db_richiesto'] deve contenere il nome completo del database su cui ricercare
	 * $filter['id_studente'] deve contenere l'id dello studente da filtrare (OBBLIGATORIO)
	 * $filter['id_marketplace'] deve contenere l'id del marketplace da filtrare (opzionale) nel caso non fosse presente verranno tirati fuori tutti i servizi
	 * $filter['id_tipo_movimento'] deve contenere l'id del tipo_movimento da filtrare (opzionale) nel caso non fosse presente verranno tirati fuori tutti i servizi
	 * $filter['data_adesione'] deve contenere la data di adesione su cui limitare l'estrazione (opzionale) nel caso non fosse presente verranno tirati fuori tutte le date
	 * $filter['tipo_risultato'] se valorizzato come "RIEPILOGO" da come risultato solo il numero delle prenotazioni e non il dettaglio, se non valorizzato prende DETTAGLIO
	 * $filter['estrai_credito'] se valorizzato come "SI" estrae anche i dati del credito
	 *
	 * @param array $filter
	 */
	public function getListaPrenotazioniGiornaliere($filter = null) {
	/*{{{ */

		if (strlen($filter['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($filter['db_richiesto']);
		}

		$array_query_filter = array();
		if ($filter['id_studente'] > 0) {
			$chiave_studenti = 'NO';
			$array_query_filter[] = " marketplace_studenti_acquisti.id_studente=" . $filter['id_studente'] . " ";
		}else{
			$chiave_studenti = 'SI';
		}
		if ($filter['id_marketplace'] > 0) {
			$array_query_filter[] = " marketplace_studenti_acquisti.id_marketplace=" . $filter['id_marketplace'] . " ";
		}else{
			$array_query_filter[] = " marketplace.caratteristiche ilike '%adesione_giornaliera%' ";
		}

		if ($filter['id_tipo_movimento'] > 0) {
			$array_query_filter[] = " marketplace.id_tipo_movimento=" . $filter['id_tipo_movimento'] . " ";
		}

		$tipo_risultato = 'DETTAGLIO';
		if ($filter['tipo_risultato'] == 'RIEPILOGO') {
			$tipo_risultato = 'RIEPILOGO';
		}


		$query_filter = "  WHERE
							marketplace.id_marketplace=marketplace_studenti_acquisti.id_marketplace
							AND
							marketplace.flag_canc=0
							AND
							marketplace_studenti_acquisti.flag_canc=0
							AND
							marketplace_studenti_acquisti.id_studente=studenti_completi.id_studente
							AND
							studenti_completi.ordinamento='0'
							AND
							studenti_completi.preiscrizioni=0 ";
		if(count($array_query_filter) > 0) {
			$query_filter .= ' AND ' . implode(' AND ', $array_query_filter) . ' ';
		}

		$sql = "SELECT 	marketplace_studenti_acquisti.*,
						marketplace.descrizione,
						marketplace.tipo,
						marketplace.id_tipo_movimento,
						marketplace.codice,
						marketplace.caratteristiche,
						marketplace.nome_sitoapp,
						marketplace.descrizione_sitoapp
					FROM marketplace_studenti_acquisti,
						marketplace,
						studenti_completi
					" . $query_filter . "
					ORDER BY
					studenti_completi.cognome,
					studenti_completi.nome,
					studenti_completi.id_studente,
					marketplace.ordinamento,
					marketplace_studenti_acquisti.id_marketplace";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		$array_dati = [];

		foreach ($rows as $key => $row) {

			$row_tradotta = $this->traduzioneCampi('misto', $row);

			if($row_tradotta['caratteristiche']['adesione_giornaliera'] == 'SI'){
				$sql_adesione = "SELECT 	id_studente,
											data_adesione,
											presenza,
											data_presenza,
											data_prenotazione
									FROM servizio_giornalieri_adesioni
									WHERE
										id_marketplace=" . $row_tradotta['id_marketplace'] ."
										AND
										servizio_giornalieri_adesioni.flag_canc = 0";

				if($chiave_studenti == 'NO'){
					$sql_adesione .= " AND id_studente=" . $filter['id_studente'] . " ";
				}else{
					$sql_adesione .= " AND id_studente=" . $row['id_studente'] . " ";
				}

				$data_adesione_filtro = explode('-', $filter['data_adesione']);
				if(count($data_adesione_filtro) == 3){
					$sql_adesione .= " AND data_adesione='" . $filter['data_adesione'] . "' ";
				}


				$query_adesione = $this->user->data->db->prepare($sql_adesione);
				$query_adesione->execute();

				$rows_adesione = $query_adesione->fetchAll(PDO::FETCH_ASSOC);

				$costo_prenotazione = $row_tradotta['caratteristiche']['costo_unitario_prenotazione'];
				if(count($rows_adesione) > 0){
					foreach ($rows_adesione as $key_adesione => $row_adesione) {
						//$array_dati[$row_tradotta['id_marketplace']][$row_adesione['data_adesione']] = 'SI';
						$prenotazione_tmp = [];
						$prenotazione_tmp['id_marketplace'] = $row_tradotta['id_marketplace'];
						$prenotazione_tmp['nome_sitoapp'] = $row_tradotta['nome_sitoapp'];
						$prenotazione_tmp['descrizione_sitoapp'] = $row_tradotta['descrizione_sitoapp'];
						$prenotazione_tmp['presenza'] = $row_adesione['presenza'];
						$prenotazione_tmp['data_presenza'] = $row_adesione['data_presenza'];
						$prenotazione_tmp['data_prenotazione'] = $row_adesione['data_prenotazione'];
						if($tipo_risultato == 'RIEPILOGO'){
							if(is_null($array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'])){
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'] = 0;
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['importo_da_pagare'] = 0;
							}
							if($row_adesione['presenza'] == 'SI'){
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['presenze']++;
								if($row_tradotta['caratteristiche']['cosa_contabilizzare'] == 'SOLO_PRESENZE'){
									$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare']++;
									$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['importo_da_pagare'] = $costo_prenotazione * $array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'];
								}
							}elseif($row_adesione['presenza'] == 'NO'){
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['assenze']++;
							}else{
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['non_definite']++;
							}
							if($row_tradotta['caratteristiche']['cosa_contabilizzare'] == 'TUTTO'){
								$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare']++;
							}
							$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['descrizione'] = $row_tradotta['descrizione'];
							$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['nome_sitoapp'] = $row_tradotta['nome_sitoapp'];
							$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['id_tipo_movimento'] = $row_tradotta['id_tipo_movimento'];
							$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['costo_unitario_prenotazione'] = $costo_prenotazione;
							$array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['importo_da_pagare'] = $costo_prenotazione * $array_dati[$row_adesione['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'];
						}else{
							if($chiave_studenti == 'NO'){
								$array_dati[$row_adesione['data_adesione']][] = $prenotazione_tmp;
							}else{
								$array_dati[$row_adesione['id_studente']][$row_adesione['data_adesione']][] = $prenotazione_tmp;
							}
						}
					}
				}else{
					$prenotazione_tmp = [];
					$prenotazione_tmp['id_marketplace'] = $row_tradotta['id_marketplace'];
					$prenotazione_tmp['nome_sitoapp'] = $row_tradotta['nome_sitoapp'];
					$prenotazione_tmp['descrizione_sitoapp'] = $row_tradotta['descrizione_sitoapp'];
					$prenotazione_tmp['presenza'] = '';
					$prenotazione_tmp['data_presenza'] = '';
					$prenotazione_tmp['data_prenotazione'] = '';
					if($tipo_risultato == 'RIEPILOGO'){
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'] = 0;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['importo_da_pagare'] = 0;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['presenze'] = 0;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['assenze'] = 0;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['non_definite'] = 0;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['descrizione'] = $row_tradotta['descrizione'];
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['nome_sitoapp'] = $row_tradotta['nome_sitoapp'];
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['id_tipo_movimento'] = $row_tradotta['id_tipo_movimento'];
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['costo_unitario_prenotazione'] = $costo_prenotazione;
						$array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['importo_da_pagare'] = $costo_prenotazione * $array_dati[$row_tradotta['id_studente']][$row_tradotta['id_marketplace']]['da_pagare'];
					}
				}
			}else{
				$array_dati['errore'] = 'Servizio senza possibilita di adesione giornaliera';
			}
		}

		$results = $array_dati;

		if (strlen($filter['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
		/*}}}*/
	}


	/**
	 * Funzione in GET
	 * recupera la lista degli delle prenotazioni giornaliere di uno studente
	 * i valori possibili sono:
	 * $filter['db_richiesto'] deve contenere il nome completo del database su cui ricercare
	 * $filter['id_studente'] deve contenere l'id dello studente da filtrare (OBBLIGATORIO)
	 * $filter['id_marketplace'] deve contenere l'id del marketplace da filtrare (opzionale) nel caso non fosse presente verranno tirati fuori tutti i servizi
	 * $filter['id_tipo_movimento'] deve contenere l'id del tipo_movimento da filtrare (opzionale) nel caso non fosse presente verranno tirati fuori tutti i servizi
	 * $filter['data_adesione'] deve contenere la data di adesione su cui limitare l'estrazione (opzionale) nel caso non fosse presente verranno tirati fuori tutte le date
	 *
	 * @param array $filter
	 */
	public function getCreditoPrenotazioniGiornaliere($filter = null) {
		/*{{{ */

			if (strlen($filter['db_richiesto']) > 0) {
				$previous = $this->user->data->SetDb($filter['db_richiesto']);
			}

			$filter['tipo_risultato'] = "RIEPILOGO";
			$riepilogo_dati = $this->getListaPrenotazioniGiornaliere($filter);

			$school = new School($this->user);
			$totale = [];
			$lista_movimenti = [];
			$data_new = [];

			foreach ($riepilogo_dati as $id_studente => $servizi) {
				foreach ($servizi as $id_marketplace => $servizio) {
					$data_new[$id_studente]['subject_id'] = $id_studente;
					$data_new[$id_studente]['subject_type'] = 'S';
					//$data_new[$id_studente]['short_response'] = 1;
					$data_new[$id_studente]['type_ids'][] = $servizio['id_tipo_movimento'];
				}
			}
			foreach ($riepilogo_dati as $id_studente => $servizi) {
				$lista_movimenti = [];
				$path = 'ccp/movement';
				$url = $path . '?' . http_build_query($data_new[$id_studente]);
				$estrazione_movimenti = [];
				$estrazione_movimenti = $school->callMc2($url);
				foreach($estrazione_movimenti['results'] as $singolo_tipo_movimento){
					if ($filter['controllo_data'] =='SI')
					{
						if (strtotime($singolo_tipo_movimento['expiration_date']) < $filter['data_verifica'])
						{
							$lista_movimenti[$singolo_tipo_movimento['type_id']][] = $singolo_tipo_movimento;
						}
					}
					else
					{
						$lista_movimenti[$singolo_tipo_movimento['type_id']][] = $singolo_tipo_movimento;
					}
				}

				foreach ($servizi as $id_marketplace => $servizio) {
					//$data = [];
					if($servizio['id_tipo_movimento'] > 0){
						//$data['subject_id'] = $id_studente;
						//$data['subject_type'] = 'S';
						//$data['short_response'] = 1;
						//$data['type_ids'][] = $servizio['id_tipo_movimento'];

						//$path = 'ccp/movement';
						//$url = $path . '?' . http_build_query($data);

						if(!is_array($totale[$id_studente][$servizio['id_tipo_movimento']]['elenco_movimenti'])){

							$sql_consolidamento = "SELECT *
												FROM servizi_giornalieri_consolidamento
												WHERE
														id_tipo_movimento=" . $servizio['id_tipo_movimento'] ."
														AND
														flag_canc = 0
														AND
														id_studente = {$id_studente}
												ORDER BY data_inizio DESC LIMIT 1";

							$query = $this->user->data->db->prepare($sql_consolidamento);
							$query->execute();

							$dati_consolidamento = $query->fetchAll(PDO::FETCH_ASSOC)[0];
							if(is_array($dati_consolidamento)){
								$data_inizio = $dati_consolidamento['data_inizio'];
								$importo_consolidamento = $dati_consolidamento['credito'];
								$dettaglio_consolidamento = [];
								$dettaglio_consolidamento['data_creazione'] = date( "Y-m-d", $dati_consolidamento['data_inizio']);
								$dettaglio_consolidamento['totale_movimento'] = $dati_consolidamento['credito'];
								$dettaglio_consolidamento['importo_pagato'] = $dati_consolidamento['credito'];
								$dettaglio_consolidamento['da_pagare'] = 0;
								$dettaglio_consolidamento['descrizione'] =$dati_consolidamento['descrizione'];
								$dettaglio_consolidamento['consolidamento'] = "SI";
								$dettaglio_consolidamento['id'] = $dati_consolidamento['id_consolidamento'];
								$totale[$id_studente][$servizio['id_tipo_movimento']]['elenco_movimenti'][] = $dettaglio_consolidamento;
							}else{
								$data_inizio = 0;
								$importo_consolidamento = 0;
							}

							$totale[$id_studente][$servizio['id_tipo_movimento']]['totale'] = $importo_consolidamento;


							//$lista_movimenti[$servizio['id_tipo_movimento']] = $school->callMc2($url);
							//foreach($lista_movimenti[$servizio['id_tipo_movimento']]['results'] as $singolo_movimento){

							foreach($lista_movimenti[$servizio['id_tipo_movimento']] as $singolo_movimento){
								if	(
										strtotime(date( "Y-m-d", $data_inizio))
										<
										strtotime(date( "Y-m-d", strtotime($singolo_movimento['creation_date'])))
									){
									$totale[$id_studente][$servizio['id_tipo_movimento']]['totale'] += $singolo_movimento['total_payments'];
									$dettaglio_movimento = [];
									$dettaglio_movimento['data_creazione'] = date( "Y-m-d", strtotime($singolo_movimento['creation_date']));
									$dettaglio_movimento['totale_movimento'] = $singolo_movimento['total'];
									$dettaglio_movimento['importo_pagato'] = $singolo_movimento['total_payments'];
									$dettaglio_movimento['da_pagare'] = $singolo_movimento['remaining'];
									$dettaglio_movimento['descrizione'] = $singolo_movimento['movement_description'];
									$dettaglio_movimento['consolidamento'] = "NO";
									$dettaglio_movimento['id'] = $singolo_movimento['id'];
									$totale[$id_studente][$servizio['id_tipo_movimento']]['elenco_movimenti'][] = $dettaglio_movimento;
									$totale[$id_studente][$servizio['id_tipo_movimento']]['descrizione'] = $singolo_movimento['type_text'];
								}
							}
						}
						$totale[$id_studente][$servizio['id_tipo_movimento']]['speso'] += $servizio['importo_da_pagare'];
					}
				}
			}

			$riepilogo_finale = [];
			foreach ($riepilogo_dati as $id_studente => $servizi) {
				foreach ($servizi as $id_marketplace => $servizio) {
					//if($servizio['id_tipo_movimento'] > 0){
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['da_pagare'] = $servizio['da_pagare'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['importo_da_pagare'] = $servizio['importo_da_pagare'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['non_definite'] = $servizio['non_definite'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['assenze'] = $servizio['assenze'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['presenze'] = $servizio['presenze'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['importo_da_pagare'] = $servizio['importo_da_pagare'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['costo_unitario_prenotazione'] = $servizio['costo_unitario_prenotazione'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['descrizione'] = $servizio['descrizione'];
						$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['servizi'][$id_marketplace]['nome_sitoapp'] = $servizio['nome_sitoapp'];
						foreach ($totale[$id_studente] as $key_id => $singolo_totale_id){
							if($key_id == $servizio['id_tipo_movimento']){
								$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['elenco_movimenti'] = $singolo_totale_id['elenco_movimenti'];
								$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['descrizione'] = $singolo_totale_id['descrizione'];
								$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['credito_totale'] = $singolo_totale_id['totale'];
								$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['credito_rimanente'] = $singolo_totale_id['totale'] - $singolo_totale_id['speso'];
							}
						}
						if($servizio['id_tipo_movimento'] == 0){
							$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['elenco_movimenti'] = [];
							$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['descrizione'] = "Nessun addebito collegato";
							$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['credito_totale'] = 0;
							$riepilogo_finale[$id_studente][$servizio['id_tipo_movimento']]['credito_rimanente'] = 0;
						}
					//}
				}
			}

			//$results['movimenti'] = $lista_movimenti;
			$results = $riepilogo_finale;

			if (strlen($filter['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}

			return $results;
			/*}}}*/
		}




	/**
	 * Funzione in GET
	 * Calcola il consolidamento dei valori di un tipo di servizio per un array di studenti
	 * i valori possibili sono:
	 * $dati_input['id_studenti'][] deve contenere l'array deli id degli studente da valutare
	 * $dati_input['db_richiesto'] deve contenere il nome del database da utilizzare se differente da quello corrente
	 * $dati_input['id_marketplace'] deve contenere l'id del servizio di marketplace da utilizzare
	 * $dati_input['data_inizio'] deve contenere il timestamp della data di inzio del controllo (compresa)
	 * $dati_input['data_fine'] deve contenere il timestamp della data di fine del controllo (compresa)
     * @param array $dati_input
	 */
	public function getConsolidamentoServizio($dati_input = null) {
		/*{{{ */

		require_once "models/class_assenze.php";

		if (strlen($dati_input['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($dati_input['db_richiesto']);
		}

		$anno_per_visualizzazione_materie = $this->user->data->GetParametroMastercom('ANNO_SCOLASTICO_ATTUALE');
		$mat_anno = explode('/',$anno_per_visualizzazione_materie);
		$anno_inizio = $mat_anno[0];
		$anno_fine = $mat_anno[1];

		foreach($dati_input['id_studenti'] as $id_studente){
			$filter = $dati_input;
			unset($filter['id_studente']);
			$filter['id_studente'] = $id_studente;
			$filter_marketplace = [];
			$filter_marketplace['id_marketplace'] = $filter['id_marketplace'];
			$filter_marketplace['id_studente'] = $id_studente;
			$abbonamenti_studente_xt = $this->getListaMarketplaceAcquisti($filter_marketplace);


			$assenze = new Assenze($this->user);

			$filtro_assenze = [];
			$filtro_assenze['id_studente'] = $filter['id_studente'];
			$filtro_assenze['data']['start'] = $filter['data_inizio'];
			$filtro_assenze['data']['end'] = $filter['data_fine'] + 82800; //aggiungo 23 ore per arrivare alla fine della giornata
			$assenze_studente = $assenze->getListaAssenze($filtro_assenze);


			//recupero info dalle assenze su struttura dedicata con chiave data
			$elenco_giorni = [];
			foreach ($assenze_studente as $key => $value) {
				$giorno = $value['data_tradotto'];
				$orario = explode(':',$value['orario']);
				$orario[0] = intval($orario[0]);
				$orario[1] = intval($orario[1]);

				$giorno_settimana_tmp = date('N',$value['data']);

				switch ($giorno_settimana_tmp) {
					case '1':
						$giorno_settimana = 'Lun';
						break;
					case '2':
						$giorno_settimana = 'Mar';
						break;
					case '3':
						$giorno_settimana = 'Mer';
						break;
					case '4':
						$giorno_settimana = 'Gio';
						break;
					case '5':
						$giorno_settimana = 'Ven';
						break;
					case '6':
						$giorno_settimana = 'Sab';
						break;
					case '7':
						$giorno_settimana = 'Dom';
						break;
				}

				if($value['tipo_assenza_tradotto']['codice'] == 'E'){
					if	(
							(intval($elenco_giorni[$giorno]['entrata']['ora']) == 0)
							or
							(
								(($elenco_giorni[$giorno]['entrata']['ora']*60)	+ ($elenco_giorni[$giorno]['entrata']['min']))
								>
								(($orario[0]*60) + $orario[1])
							)
						)
					{
						$elenco_giorni[$giorno]['data'] = $value['data'];
						$elenco_giorni[$giorno]['giorno_settimana'] = $giorno_settimana;
						$elenco_giorni[$giorno]['entrata']['ora'] = $orario[0];
						$elenco_giorni[$giorno]['entrata']['min'] = $orario[1];
						$elenco_giorni[$giorno]['entrata']['minuti_totali'] = $orario[0]*60 + $orario[1];
					}
				}
				if($value['tipo_assenza_tradotto']['codice'] == 'U'){
					if	(
						(($elenco_giorni[$giorno]['uscita']['ora']*60)	+ ($elenco_giorni[$giorno]['uscita']['min']))
						<
						(($orario[0]*60) + $orario[1])
					)
					{
						$elenco_giorni[$giorno]['data'] = $value['data'];
						$elenco_giorni[$giorno]['giorno_settimana'] = $giorno_settimana;
						$elenco_giorni[$giorno]['uscita']['ora'] = $orario[0];
						$elenco_giorni[$giorno]['uscita']['min'] = $orario[1];
						$elenco_giorni[$giorno]['uscita']['minuti_totali'] = $orario[0]*60 + $orario[1];
					}
				}
			}


			$elenco_giorni_finali = [];
			$elenco_giorni_finali['giorni'] = $elenco_giorni;

			foreach ($elenco_giorni_finali['giorni'] as $giorno => $dati) {
				foreach ($abbonamenti_studente_xt as $abbonamento => $dati_abbonamento) {
					if(	($dati_abbonamento['validita_inizio'] <= $dati['data'])
						and
						($dati['data'] <= $dati_abbonamento['validita_fine'])){
							foreach ($dati_abbonamento['opzioni'][$dati['giorno_settimana']]['opzioni'] as $opzione) {
							$orario_inizio = explode(':',$opzione['ora_inizio']);
							$orario_inizio[0] = intval($orario_inizio[0]);
							$orario_inizio[1] = intval($orario_inizio[1]);
							$minuti_totali_entrata = $orario_inizio[0]*60 + $orario_inizio[1];
							$orario_fine = explode(':',$opzione['ora_fine']);
							$orario_fine[0] = intval($orario_fine[0]);
							$orario_fine[1] = intval($orario_fine[1]);
							$minuti_totali_uscita = $orario_fine[0]*60 + $orario_fine[1];
							if(
								($dati['entrata']['minuti_totali'] > 0)
								and
								($dati['entrata']['minuti_totali'] < $minuti_totali_entrata)
							)
							{
								$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] = $minuti_totali_entrata - $dati['entrata']['minuti_totali'];
								//modifica per myschool ticino
								if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] / 60) > 1){
									if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60) > 10){
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60) + 60;
									}else{
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60);
									}
								}else{
									if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60) > 15){
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60) + 60;
									}else{
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % 60);
									}
								}
								//$elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata'] % $opzione['arrotondamento_minuti']);
								$elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_entrata'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_entrata_arrotondato'] * $opzione['costo_minuto_extratime'];
								$elenco_giorni_finali['valori_scostamento']['entrata'] += $elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_entrata'];
								$elenco_giorni_finali['valori_scostamento']['totali'] += $elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_entrata'];
							}
							if(
								($dati['uscita']['minuti_totali'] > 0)
								and
								($dati['uscita']['minuti_totali'] > $minuti_totali_uscita)
							)
							{
								$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] = $dati['uscita']['minuti_totali'] - $minuti_totali_uscita;
								if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] / 60) > 1){
									if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60) > 10){
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60) + 60;
									}else{
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60);
									}
								}else{
									if(($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60) > 15){
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60) + 60;
									}else{
										$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % 60);
									}
								}
								//$elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] - ($elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita'] % $opzione['arrotondamento_minuti']);
								$elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_uscita'] = $elenco_giorni_finali['giorni'][$giorno]['scostamento_uscita_arrotondato'] * $opzione['costo_minuto_extratime'];
								$elenco_giorni_finali['valori_scostamento']['uscita'] += $elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_uscita'];
								$elenco_giorni_finali['valori_scostamento']['totali'] += $elenco_giorni_finali['giorni'][$giorno]['valore_scostamento_uscita'];
							}
						}
					}
				}
			}
			$elenco_totale_giorni_finali[$id_studente] = $elenco_giorni_finali;
		}

		$results = $elenco_totale_giorni_finali;

		if (strlen($dati_input['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $results;
		/*}}}*/
	}

	/**
	 * Funzione in PUT
	 * inserisce una nuova assenza in servizio_giornalieri_adesioni sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['data_adesione'] deve contenere la data dell'adesione in formato ISO con separatore trattini es: 2021-01-23 - obbligatorio
	 * $dati['id_studente'] deve contenere l'id dello studente abbinato - opzionale, se non precisato effettua l'operazione per tutti gli studenti prenotati per il giorno
	 * $dati['id_marketplace'] deve contenere l'id_marketplace da verificare - obbligatorio
	 * $dati['db_richiesto'] deve contenere il nome del database da utilizzare se differente da quello corrente - opzionale
	 *
	 * @param array $dati
	 */
	public function inserisciAssenzaGiornalieraServizio($dati)
	{
		require_once "models/class_notifiche.php";
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		if(count($errori) == 0){
			$id_marketplace = intval($dati['id_marketplace']);
			$id_studente = intval($dati['id_studente']);
			$data_adesione = $dati['data_adesione'];
			if((count(explode('-', $dati['data_adesione'])) == 3) && $id_marketplace > 0){
				$filter = [];

				if($id_studente > 0){
					$filter['id_studente'] = $id_studente;
				}
				$filter['id_marketplace'] = $id_marketplace;
				$filter['data_adesione'] = $data_adesione;

				$prenotazioni_giornaliere = $this->getListaPrenotazioniGiornaliere($filter);

				foreach($prenotazioni_giornaliere as $id_studente_riga => $riga){
					foreach($riga[$data_adesione] as $singolo_servizio){

						if(($singolo_servizio['presenza'] == "") && ($singolo_servizio['id_marketplace'] == $id_marketplace)){
							$sql = "UPDATE servizio_giornalieri_adesioni
										SET presenza = 'NO', data_presenza = " . time() . "
										WHERE
											id_marketplace = " . $id_marketplace . "
											AND
											data_adesione = '" . $data_adesione . "'
											AND
											id_studente = " . $id_studente_riga;

							$query = $this->user->data->db->prepare($sql);
							$query->execute();
							$mat_oggetti = [
								"id_studente" => $id_studente,
								"id_marketplace" => $id_marketplace,
								"data_adesione" => $data_adesione
							];

							$this->user->data->inserisciLog($mat_oggetti, 'servizio_giornalieri_adesioni', $current_user, "NEXTAPI", "MODIFICA");

							//creazione notifica -- TODO verificare parametro in servizio per l'invio
							$notifiche = new Notifiche($this->user);
							$nome_database_attuale_tmp = $this->user->data->getParametro('mastercom_corrente');
							$nome_database_attuale = explode('_', $nome_database_attuale_tmp);
							if(count($nome_database_attuale) == 3){
								$anno_scolastico_attuale = $nome_database_attuale[1] . "/" . $nome_database_attuale[2];
							}else{
								$anno_scolastico_attuale = "2021/2022";
							}
							$parametri = [
								"id_oggetto"            =>  $id_marketplace . '#' . $data_adesione . '#' . $id_studente_riga,
								"tipo_oggetto"          =>  "assenze_servizi_giornalieri",
								"anno_riferimento"      =>  $anno_scolastico_attuale,
								"id_studenti"             =>  ["0"        =>  $id_studente_riga],
								"notifica_studenti"     =>  "SI",
								"notifica_parenti"      =>  "SI",
								"notifica_docenti"      =>  "NO",
								"notifica_dirigenti"    =>  "NO"
							];

							$notifiche->inserisciNotifica($parametri);
						}
					}
				}
			}else{
				$result = $errori;
			}

			if (strlen($nome_db) > 0) {
				$this->user->data->SetDb($previous);
			}
		}

		return $result;
	}

	/**
	 * Funzione in POST
	 * inserisce la presenza in servizio_giornalieri_adesioni sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * id_studente: l'id dello studente per il quale effettuare la presenza
	 * id_marketplace: l'id_marketplace dell'elemento per il quale effettuare la presenza
	 * data_adesione: in formato Y-m-d che identifichi l'adesione al servizio
	 * data_presenza (facoltativo): in timestamp per identificare la data effettiva di presenza. Se non specificato viene utilizzata quella giornaliera
	 *
	 * @param array $dati
	 */
	public function inserisciPresenzaGiornalieraServizioStudente($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		$data_presenza = time();

		if ($dati['data_presenza'] > 0){
			$data_presenza = $dati['data_presenza'];
		}

		if(count($errori) == 0){
			if(
				(
					(intval($dati['data_adesione']) > 0) || (count(explode('-', $dati['data_adesione'])) == 3)
				)
				&& (intval($dati['id_studente']) > 0)
				&& (intval($dati['id_marketplace']) > 0)
			){
				if(count(explode('-', $dati['data_adesione'])) == 3){
					$data_adesione = trim($dati['data_adesione']);
				}else{
					$data_adesione = date('Y-m-d', intval($dati['data_adesione']));
				}

				$sql = "
					UPDATE servizio_giornalieri_adesioni
					SET presenza = 'SI',
						data_presenza = {$data_presenza}
					WHERE id_marketplace = {$dati['id_marketplace']}
						AND id_studente = {$dati['id_studente']}
						AND data_adesione = '{$data_adesione}'
				";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();

				$sql = "
					SELECT *
					FROM servizio_giornalieri_adesioni
					WHERE id_marketplace = {$dati['id_marketplace']}
						AND id_studente = {$dati['id_studente']}
						AND data_adesione = '{$data_adesione}'
				";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();

				$mat_oggetti = [
					"id_studente" => $dati['id_studente'],
					"id_marketplace" => $dati['id_marketplace'],
					"data_adesione" => $data_adesione
				];

				$this->user->data->inserisciLog($mat_oggetti, 'servizio_giornalieri_adesioni', $current_user, "NEXTAPI", "MODIFICA");

				$results = $query->fetchAll(PDO::FETCH_ASSOC)[0];
			}else{
				$errori[] = "data_adesione ({$dati['data_adesione']}), id_marketplace ({$dati['id_marketplace']}) o id_studente ({$dati['id_studente']}) non corretti";
			}
		}
		if(count($errori) > 0){
			$results['errori'] = $errori;
		}

		return $results;
	}

	/**
	 * Funzione in DELETE
	 * elimina la presenza in servizio_giornalieri_adesioni sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * id_studente: l'id dello studente per il quale effettuare la presenza
	 * id_marketplace: l'id_marketplace dell'elemento per il quale effettuare la presenza
	 * data_adesione: in formato Y-m-d che identifichi l'adesione al servizio
	 *
	 * @param array $dati
	 */
	public function eliminaPresenzaGiornalieraServizioStudente($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		if(count($errori) == 0){
			if(
				(
					(intval($dati['data_adesione']) > 0) || (count(explode('-', $dati['data_adesione'])) == 3)
				)
				&& (intval($dati['id_studente']) > 0)
				&& (intval($dati['id_marketplace']) > 0)
			){
				if(count(explode('-', $dati['data_adesione'])) == 3){
					$data_adesione = trim($dati['data_adesione']);
				}else{
					$data_adesione = date('Y-m-d', intval($dati['data_adesione']));
				}

				$sql = "
					SELECT *
					FROM servizio_giornalieri_adesioni
					WHERE id_marketplace = {$dati['id_marketplace']}
						AND id_studente = {$dati['id_studente']}
						AND data_adesione = '{$data_adesione}'
				";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();
				$presenza_attuale = $query->fetchAll(PDO::FETCH_ASSOC)[0];

				if (abs($presenza_attuale['data_prenotazione'] - $presenza_attuale['data_presenza']) <= 5) {
					// prenotazione e presenza effettuate nello stesso momento
					$sql = "
						DELETE FROM servizio_giornalieri_adesioni
						WHERE id_marketplace = {$dati['id_marketplace']}
							AND id_studente = {$dati['id_studente']}
							AND data_adesione = '{$data_adesione}'
					";
					$query = $this->user->data->db->prepare($sql);
					$query->execute();
				} else {
					// prenotazione e presenza effettuate in momenti diversi
					$sql = "
						UPDATE servizio_giornalieri_adesioni
						SET presenza = 'NO',
							data_presenza = 0
						WHERE id_marketplace = {$dati['id_marketplace']}
							AND id_studente = {$dati['id_studente']}
							AND data_adesione = '{$data_adesione}'
					";
					$query = $this->user->data->db->prepare($sql);
					$query->execute();
				}

				$sql = "
					SELECT *
					FROM servizio_giornalieri_adesioni
					WHERE id_marketplace = {$dati['id_marketplace']}
						AND id_studente = {$dati['id_studente']}
						AND data_adesione = '{$data_adesione}'
				";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();

				$results = $query->fetchAll(PDO::FETCH_ASSOC)[0];
			}else{
				$errori[] = "data_adesione ({$dati['data_adesione']}), id_marketplace ({$dati['id_marketplace']}) o id_studente ({$dati['id_studente']}) non corretti";
			}
		}
		if(count($errori) > 0){
			$results['errori'] = $errori;
		}

		return $results;
	}

	/**
	 * Funzione in PUT
	 * inserisce un nuovo elemanto in servizio_giornalieri_adesioni sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['tipo_operazione'] deve contenere il tipo di operazione che si desidera fare "INSERISCI" o "ELIMINA" - obbligatorio
	 * $dati['data_inizio'] deve contenere la data di inizio dell'adesione in questione in formato timestamp, verrà  però salvata in formato ISO con separatore trattini es: 2021-01-23 - obbligatorio
	 * $dati['data_fine'] deve contenere la data di fine dell'adesione in questione in formato timestamp, verrà  però salvata in formato ISO con separatore trattini es: 2021-01-23 - obbligatorio
	 * $dati['id_studente'] deve contenere l'id dello studente abbinato - obbligatorio
	 * $dati['id_marketplace'] deve contenere l'id_marketplace a cui lo studente è abbinato (se lo studente non risulta abbinato non verrà  inserito il record) - obbligatorio
	 *
	 * @param array $dati
	 */
	public function aggiornaAdesioniPeriodoServizioGiornalieroStudente($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
			$nome_database_attuale = explode('_', $nome_db);
			if(count($nome_database_attuale) == 3){
				$anno_scolastico_attuale = $nome_database_attuale[1] . "/" . $nome_database_attuale[2];
			}
		}else{
			$nome_database_attuale_tmp = $this->user->data->getParametro('mastercom_corrente');
			$nome_database_attuale = explode('_', $nome_database_attuale_tmp);
			if(count($nome_database_attuale) == 3){
				$anno_scolastico_attuale = $nome_database_attuale[1] . "/" . $nome_database_attuale[2];
			}
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		$result = [];
		$data_inizio_adesione = 0;
		$data_fine_adesione = 0;
		$tipo_operazione = "";
		$id_marketplace = 0;
		$id_studente = 0;
		$data_prenotazione = time();

		if($dati["tipo_operazione"] == "INSERISCI"){
			$tipo_operazione = "INSERISCI";
		}elseif($dati["tipo_operazione"] == "ELIMINA"){
			$tipo_operazione = "ELIMINA";
		}else{
			$errori[] = "Non è stato definito il tipo di operazione. ";
		}

		if((intval($dati['data_inizio']) > 0) || (count(explode('-', $dati['data_inizio'])) == 3)){
			if(count(explode('-', $dati['data_inizio'])) == 3){
				$mat_data_inizio = explode('-', $dati['data_inizio']);
				$data_inizio_adesione = mktime(0,0,0,$mat_data_inizio[1], $mat_data_inizio[2], $mat_data_inizio[0]);
			}else{
				$data_inizio_adesione = intval($dati['data_inizio']);
			}
		}else{
			$errori[] = "Non è stata definita la data_inizio. ";
		}

		if((intval($dati['data_fine']) > 0) || (count(explode('-', $dati['data_fine'])) == 3)){
			if(count(explode('-', $dati['data_fine'])) == 3){
				$mat_data_fine = explode('-', $dati['data_fine']);
				$data_fine_adesione = mktime(0,0,0,$mat_data_fine[1], $mat_data_fine[2], $mat_data_fine[0]);
			}else{
				$data_fine_adesione = intval($dati['data_fine']);
			}
			if($data_inizio_adesione > $data_fine_adesione){
				$errori[] = "Data inizio è successiva a data fine. ";
			}
		}else{
			$errori[] = "Non è stata definita la data_fine. ";
		}

		if(intval($dati['id_marketplace']) > 0){
			$id_marketplace = intval($dati['id_marketplace']);
			$dati_marketplace = $this->getListaMarketplace($id_marketplace)[$id_marketplace];
		}else{
			$errori[] = "Non è stato definito l'id_marketplace. ";
		}

		if(intval($dati['id_studente']) > 0){
			$id_studente = intval($dati['id_studente']);
		}else{
			$errori[] = "Non è stato definito l'id_studente. ";
		}

		$results['cancellazioni'] = 0;
		if(count($errori) == 0){
			$filtro_acquisti = [];
			$filtro_acquisti['id_studente'] = $id_studente;
			$filtro_acquisti['id_marketplace'] = $id_marketplace;
			$dati_acquisto = $this->getListaMarketplaceAcquisti($filtro_acquisti);
			$payload_json = [];
			if($tipo_operazione == "INSERISCI"){
				$payload_json["preadesioni_servizio_giornaliero"] = "SI";
			}elseif($tipo_operazione == "ELIMINA"){
				$payload_json["preadesioni_servizio_giornaliero"] = "NO";
			}
			$payload_json = json_encode($payload_json);

			$sql = "UPDATE marketplace_studenti_acquisti
						SET opzioni = '" . $payload_json . "'
						WHERE
							id_studente = " . $id_studente . "
							AND
							id_marketplace = " . $id_marketplace . "
							AND
							flag_canc = 0";

			$query = $this->user->data->db->prepare($sql);
			$query->execute();

			$school_data = new School($this->user);
			$filter['tipo_export'] = 'festivita';
			$filter['query']['anno_scolastico'] = $anno_scolastico_attuale;
			$festivita =  $school_data->getList($filter);

			$data_adesione = $data_inizio_adesione;
			do {

				$giorno_settimana_tmp = date('N',$data_adesione);
				switch ($giorno_settimana_tmp) {
					case '1':
						$giorno_settimana = 'lun';
						break;
					case '2':
						$giorno_settimana = 'mar';
						break;
					case '3':
						$giorno_settimana = 'mer';
						break;
					case '4':
						$giorno_settimana = 'gio';
						break;
					case '5':
						$giorno_settimana = 'ven';
						break;
					case '6':
						$giorno_settimana = 'sab';
						break;
					case '7':
						$giorno_settimana = 'dom';
						break;
				}

				if($dati_marketplace['caratteristiche']['giornate_attive'][$giorno_settimana] == 'SI'){
					$data_adesione_query = date('Y-m-d', $data_adesione);

					$sql = "DELETE FROM servizio_giornalieri_adesioni
									WHERE
										id_studente = " . $id_studente . "
										AND
										id_marketplace = " . $id_marketplace . "
										AND
										data_adesione = '" . $data_adesione_query . "'";

					$query = $this->user->data->db->prepare($sql);
					$query->execute();
					$results['cancellazioni']++;

					if(($tipo_operazione == "INSERISCI") && ($festivita[$data_adesione_query] == 'A')){
						$sql = "INSERT INTO servizio_giornalieri_adesioni
							(id_studente, id_marketplace, data_adesione, data_prenotazione) VALUES (" . $id_studente . ", " . $id_marketplace . ",'" . $data_adesione_query . "', " . $data_prenotazione . ")";

						$query = $this->user->data->db->prepare($sql);
						$query->execute();

						$mat_oggetti = [
							"id_studente" => $id_studente,
							"id_marketplace" => $id_marketplace,
							"data_adesione" => $data_adesione_query
						];

						$this->user->data->inserisciLog($mat_oggetti, 'servizio_giornalieri_adesioni', $current_user, "NEXTAPI", "INSERIMENTO");

						$results['adesioni'][] = $mat_oggetti;
					}
				}
				$data_adesione = mktime(0,0,0,date('m',$data_adesione),date('d',$data_adesione)+1,date('Y',$data_adesione));
			} while ($data_adesione <= $data_fine_adesione);
		}
		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}
		if(count($errori) > 0){
			$results['errori'] = $errori;
		}

		return $results;
	}

	/**
	 * {"id_studente": 1006726,
	 *  "id_tipo_movimento": 43,
	 *	"credito_iniziale": 0.0,
	 * 	"data_consolidamento": 4584759857,
	 * 	"descrizione":"consolidamento periodo dal 01-01-2021 al 15-03-2021",
	 *	"note":""}
	 * Funzione in POST
	 * inserisce un nuovo elemento in consolidamento servizi sulla base dei dati passati in $dati
	 * i dati utilizzati sono:
	 * $dati['id_studente'] deve contenere l'id dello studente di cui impostare il consolidamento o l'array degli id studenti da consolidare allo stesso valore
	 * $dati['id_tipo_movimento'] deve contenere l'id del tipo di movimento di cui impostare il consolidamento
	 * $dati['credito_iniziale'] deve contenere il valore in float della rimanenza a cui si vuole portare il credito dei servizi che afferiscono al tipo movimento indicato precedentemente (per un consolidamento generico 0.0)
	 * $dati['data_consolidamento'] la data di riferimento per il consildamento in timestamp
	 * $dati['descrizione'] una breve descrizione dell'operazione
	 * $dati['note'] note varie

     * @param array $dati
	 */
	public function inserisciConsolidamento($dati)
	{
		$nome_db = $dati['db_richiesto'];
		unset($dati['db_richiesto']);
		if (strlen($nome_db) > 0) {
			$previous = $this->user->data->SetDb($nome_db);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUserOrMc2User(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		$array_id_studenti = [];
		if(!is_array($dati['id_studente'])){
			if(is_numeric($dati['id_studente'])){
				$array_id_studenti[] = $dati['id_studente'];
			}else{
				$errori[] = "impossibile procedere, non è stato specificato nemmeno un id_studente valido";
			}
		}else{
			$array_id_studenti = $dati['id_studente'];
		}

		if(is_numeric($dati['id_tipo_movimento'])){
			$id_tipo_movimento = $dati['id_tipo_movimento'];
		}else{
			$errori[] = "impossibile procedere, non è stato specificato un id_tipo_movimento valido";
		}

		$credito_iniziale = floatval($dati['credito_iniziale']);
		$data_consolidamento = intval($dati['data_consolidamento']);
		$descrizione = $this->user->data->EncodeField(trim($dati['descrizione']));
		$note = $this->user->data->EncodeField(trim($dati['note']));

		if(count($errori) == 0){
			$cont = 0;
			foreach($array_id_studenti as $id_studente_tmp){
				$id_studente = intval($id_studente_tmp);
				$id_consolidamento = "";

				if(($id_studente > 0) && ($data_consolidamento > 0)){
					$sql = "  INSERT INTO servizi_giornalieri_consolidamento (
						id_studente,
						id_tipo_movimento,
						tipo,
						descrizione,
						note,
						credito,
						data_inizio,
						chi_inserisce,
						data_inserimento,
						tipo_inserimento,
						chi_modifica,
						data_modifica,
						tipo_modifica
						)
					VALUES (
						$id_studente,
						$id_tipo_movimento,
						'MANUALE',
						'" . $descrizione . "',
						'" . $note . "',
						$credito_iniziale,
						$data_consolidamento,
						$current_user,
						" . time() . ",
						'INTERFACCIA',
						$current_user,
						" . time() . ",
						'INTERFACCIA'
						)
					RETURNING id_consolidamento
					";

					$query = $this->user->data->db->prepare($sql);
					$query->execute();
					$id_consolidamento = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_consolidamento'];
					if($id_consolidamento > 0){
						$cont++;
					}

					$mat_oggetti = [
							"id_consolidamento" => $id_consolidamento
						];

					$this->user->data->inserisciLog($mat_oggetti, 'servizi_giornalieri_consolidamento', $current_user, "NEXTAPI", "INSERIMENTO");
				}
			}
		}

		if (strlen($nome_db) > 0) {
			$this->user->data->SetDb($previous);
		}

		$tot_stud_processati = count($array_id_studenti) - $cont;
		$errori[] = "errori inserimento su id_corretti = " . $tot_stud_processati;
		$mat_result = [
			"errori" => $errori,
			"inseriti" => $cont
		];
		return $mat_result;
	}

	public function getListaArticoliVecchioNegozio($id_studente, $anno_scolastico){
		$ini_conf = parse_ini_file('/etc/mastercom/configuration.ini',true);
		require_once "models/class_mc2.php";
		$mc2 = new Mc2($this->user);

		$articoli = [];
		$modalita_pagamento	= [];

		// estraggo i dati dello studente
		$sql = "SELECT id_studente, cognome, nome, id_classe, id_indirizzo
			FROM studenti_completi
			WHERE ordinamento = '0'
				AND id_studente = {$id_studente}";
		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$studente = $query->fetchAll(PDO::FETCH_ASSOC)[0];

		// passo su mc2
		$previous = $this->user->data->getCurrentDb();

		try {
			$host = 'localhost';
			$dbname = 'mastercom2';
			$user = 'postgres';
			$password = 'postgres';
			$port = $ini_conf['pgsql']['port'];

			$pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $user, $password, [
				PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
			]);

			// verifico se i pagamenti online sono attivi
			$pagamenti_online_attivi = false;
			$stripe_attivo = false;
			$satispay_attivo = false;

			// STRIPE
			$sql = "SELECT * FROM parameter WHERE (name ilike 'STRIPE_PUBLIC_KEY' OR name ilike 'STRIPE_SECRET_KEY') AND value != ''";
			$query = $pdo->query($sql);
			$query->execute();
			$stripe_params = $query->fetchAll(PDO::FETCH_ASSOC);
			foreach ($stripe_params as $param) {
				$stripe[$param['name']] = $param['value'];
			}

			if ($stripe['STRIPE_PUBLIC_KEY'] != '' && $stripe['STRIPE_SECRET_KEY'] != '') {
				$pagamenti_online_attivi = true;
				$stripe_attivo = true;
				$modalita_pagamento[] = 'STRIPE';
			}

			// SATISPAY
			$satispay = $mc2->getInfoSatispay($studente['id_indirizzo']);

			if ($satispay['id_satispay'] > 0){
				$pagamenti_online_attivi = true;
				$satispay_attivo = true;
				$modalita_pagamento[] = 'SATISPAY';
			}

			if ($pagamenti_online_attivi) {
				// estraggo gli articoli
				$anno_scolastico = str_replace('_', '/', $anno_scolastico);
				$sql = "SELECT * FROM ccp_type WHERE online_payment_status IN (1,2) AND school_year = '$anno_scolastico'";
				$query = $pdo->query($sql);
				$query->execute();
				$elenco_articoli = $query->fetchAll(PDO::FETCH_ASSOC);

				// estraggo la valuta
				$sql = "SELECT * FROM parameter WHERE name ilike 'CURRENCY_UNIT'";
				$query = $pdo->query($sql);
				$query->execute();
				$valuta = $query->fetchAll(PDO::FETCH_ASSOC)[0]['value'];
				if ($valuta == ''){
					$valuta = '€';
				}

				if (count($elenco_articoli) > 0){
					foreach ($elenco_articoli as $articolo) {
						// estraggo la somma totale delle rateizzazioni dell'articolo
						$sql = "SELECT sum(value) FROM ccp_type_step WHERE ccp_type = {$articolo['id']}";
						$query = $pdo->query($sql);
						$query->execute();
						$totale = $query->fetchAll(PDO::FETCH_ASSOC)[0]['sum'];
						$totale_in_valuta = $valuta . ' ' . number_format($totale, 2, ',', '');
						if (strtoupper($valuta) == 'CHF'){
							$totale_in_valuta = $valuta . ' ' . number_format($totale, 2, ',', "'");
						}

						if ($totale <= 0){
							continue;
						}

						$articoli[] = [
							"id" => $articolo['id'],
							"tipo" => "tipo_movimento",
							"titolo" => $articolo['name'] . ": " . $totale_in_valuta,
							"sottotitolo" => "MASTERCOM",
							"descrizione" => $articolo['name'],
							"data_scadenza" => "",
							"dettaglio" => "<p>Utilizzare il metodo di pagamento concordato</p>",
							"stato" => null,
							"prezzo" => (float) $totale,
							"totale" => null,
							"ordinamento" => 0,
							"pagamento_cc" => $stripe_attivo,
							"pagamento_satispay" => $satispay_attivo,
							"allegati" => [],
							"pagato" => false,
							"data_creazione" => "",
							"scaduto" => false,
							"credito" => false,
							"descrizione_sconto" => null,
							"lista_sconti" => null,
							"descrizione_importo" => $totale_in_valuta,
							"descrizione_scadenza" => null
						];
					}
				}
			}
		} catch (PDOException $e) {
			echo "Errore: " . $e->getMessage();
		}

		$this->user->data->SetDb('previous');

		$result = [
			"articoli" => $articoli,
			"modalita_pagamento" => $modalita_pagamento
		];

		return $result;
	}

	/**
	 * Funzione in GET
	 * Estrae la lista degli oggetti negozio acquistabili per uno studente specifico
	 * Considera filtri di visibilità, disponibilità, classi, indirizzi e periodi di validità
	 *
	 * @param array $filter parametri di filtro
	 * $filter['id_studente'] - ID dello studente (obbligatorio)
	 * $filter['categoria'] - categoria da filtrare (default: NEGOZIO)
	 * $filter['db_richiesto'] - database specifico (opzionale)
	 * @return array lista oggetti negozio disponibili per lo studente
	 */
	public function estraiListaOggettiNegozio($filter = null) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($filter['id_studente']) || !is_numeric($filter['id_studente']) || $filter['id_studente'] <= 0) {
			$errori['id_studente'] = 'ID studente obbligatorio e deve essere numerico positivo';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		if (strlen($filter['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($filter['db_richiesto']);
		}

		$id_studente = $filter['id_studente'];
		$categoria = $filter['categoria'] ?? 'NEGOZIO';
		$timestamp_corrente = time();

		// Recupera informazioni studente per filtri di classe/indirizzo
		$sql_studente = "SELECT id_studente, id_classe, id_indirizzo, id_scuola
						FROM studenti_completi
						WHERE id_studente = :id_studente AND flag_canc = 0";

		$query_studente = $this->user->data->db->prepare($sql_studente);
		$query_studente->bindParam(':id_studente', $id_studente);
		$query_studente->execute();
		$studente = $query_studente->fetch(PDO::FETCH_ASSOC);

		if (!$studente) {
			return ['esito' => 'ERRORE', 'errori' => ['studente' => 'Studente non trovato']];
		}

		// Query principale per oggetti marketplace
		// NOTA: Per ora gestiamo solo visibilità 'pubblico'.
		// I filtri avanzati per 'privato' e 'filtrato' saranno implementati quando necessario.
		$sql = "SELECT m.*,
				       COALESCE(m.caratteristiche, '') as caratteristiche_json
				FROM marketplace m
				WHERE m.flag_canc = 0
				  AND m.categoria = :categoria
				  AND m.validita_inizio <= :timestamp_corrente
				  AND (m.validita_fine = -1 OR m.validita_fine >= :timestamp_corrente)
				  AND m.visibilita = 'pubblico'
				ORDER BY m.ordinamento, m.descrizione";

		$query = $this->user->data->db->prepare($sql);
		$query->bindParam(':categoria', $categoria);
		$query->bindParam(':timestamp_corrente', $timestamp_corrente);
		$query->execute();

		$oggetti = $query->fetchAll(PDO::FETCH_ASSOC);
		$risultato = [];

		foreach ($oggetti as $oggetto) {
			// Decodifica caratteristiche JSON
			$caratteristiche = [];
			if (!empty($oggetto['caratteristiche_json'])) {
				$caratteristiche = json_decode($oggetto['caratteristiche_json'], true) ?: [];
			}

			// Verifica disponibilità specifica per oggetti negozio
			$disponibile = true;
			if (isset($caratteristiche['oggetto_negozio'])) {
				$obj_negozio = $caratteristiche['oggetto_negozio'];

				// Verifica disponibilità per classi specifiche
				if (isset($obj_negozio['oggetto_disponibile_per']) && $obj_negozio['oggetto_disponibile_per'] === 'solo_selezionati') {
					if (isset($obj_negozio['disponibile_per_classi']) && is_array($obj_negozio['disponibile_per_classi'])) {
						$classe_trovata = false;
						foreach ($obj_negozio['disponibile_per_classi'] as $classe_id => $classe_nome) {
							if ($classe_id == $studente['id_classe']) {
								$classe_trovata = true;
								break;
							}
						}
						if (!$classe_trovata) {
							$disponibile = false;
						}
					}
				}

				// Verifica periodo sconto se presente
				if (isset($obj_negozio['sconto_valido_dal']) && isset($obj_negozio['sconto_valido_al'])) {
					$sconto_dal = strtotime($obj_negozio['sconto_valido_dal']);
					$sconto_al = strtotime($obj_negozio['sconto_valido_al']);
					$oggetto['sconto_attivo'] = ($timestamp_corrente >= $sconto_dal && $timestamp_corrente <= $sconto_al);
				} else {
					$oggetto['sconto_attivo'] = false;
				}
			}

			if ($disponibile) {
				$oggetto_tradotto = $this->traduzioneCampi('marketplace', $oggetto);
				$oggetto_tradotto['caratteristiche'] = $caratteristiche;
				$risultato[$oggetto['id_marketplace']] = $oggetto_tradotto;
			}
		}

		if (strlen($filter['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return [
			'esito' => 'OK',
			'oggetti_negozio' => $risultato,
			'studente' => $studente
		];

		/*}}}*/
	}

	/**
	 * Funzione in POST
	 * Inserisce un articolo nel carrello (stato CARRELLO)
	 *
	 * @param array $dati dati dell'articolo da inserire nel carrello
	 * $dati['id_studente'] - ID dello studente (obbligatorio)
	 * $dati['id_marketplace'] - ID dell'articolo marketplace (obbligatorio)
	 * $dati['opzioni'] - opzioni selezionate per l'articolo (opzionale)
	 * $dati['db_richiesto'] - database specifico (opzionale)
	 * @return mixed ID acquisto creato o array errori
	 */
	public function inserisciArticoloCarrello($dati) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($dati['id_studente']) || !is_numeric($dati['id_studente']) || $dati['id_studente'] <= 0) {
			$errori['id_studente'] = 'ID studente obbligatorio e deve essere numerico positivo';
		}

		if (!isset($dati['id_marketplace']) || !is_numeric($dati['id_marketplace']) || $dati['id_marketplace'] <= 0) {
			$errori['id_marketplace'] = 'ID marketplace obbligatorio e deve essere numerico positivo';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		if (strlen($dati['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($dati['db_richiesto']);
		}

		// Verifica che l'articolo marketplace esista e sia disponibile
		$sql_verifica = "SELECT * FROM marketplace
						WHERE id_marketplace = :id_marketplace
						  AND flag_canc = 0
						  AND categoria = 'NEGOZIO'
						  AND validita_inizio <= :timestamp_corrente
						  AND (validita_fine = -1 OR validita_fine >= :timestamp_corrente)";

		$timestamp_corrente = time();
		$query_verifica = $this->user->data->db->prepare($sql_verifica);
		$query_verifica->bindParam(':id_marketplace', $dati['id_marketplace']);
		$query_verifica->bindParam(':timestamp_corrente', $timestamp_corrente);
		$query_verifica->execute();
		$articolo = $query_verifica->fetch(PDO::FETCH_ASSOC);

		if (!$articolo) {
			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return ['esito' => 'ERRORE', 'errori' => ['articolo' => 'Articolo non trovato o non disponibile']];
		}

		// Verifica che non esista già nel carrello
		$sql_carrello = "SELECT id_acquisto FROM marketplace_studenti_acquisti
						WHERE id_studente = :id_studente
						  AND id_marketplace = :id_marketplace
						  AND stato_ordine = 'CARRELLO'
						  AND flag_canc = 0";

		$query_carrello = $this->user->data->db->prepare($sql_carrello);
		$query_carrello->bindParam(':id_studente', $dati['id_studente']);
		$query_carrello->bindParam(':id_marketplace', $dati['id_marketplace']);
		$query_carrello->execute();
		$esistente = $query_carrello->fetch(PDO::FETCH_ASSOC);

		if ($esistente) {
			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return ['esito' => 'ERRORE', 'errori' => ['duplicato' => 'Articolo già presente nel carrello']];
		}

		// Prepara i dati per l'inserimento
		$dati_acquisto = [
			'id_studente' => $dati['id_studente'],
			'id_marketplace' => $dati['id_marketplace'],
			'stato_ordine' => 'CARRELLO',
			'contabilizzato' => 'NO',
			'validita_inizio' => $timestamp_corrente,
			'validita_fine' => $timestamp_corrente + (365 * 24 * 60 * 60), // 1 anno di default
			'opzioni' => $dati['opzioni'] ?? []
		];

		if (strlen($dati['db_richiesto']) > 0) {
			$dati_acquisto['db_richiesto'] = $dati['db_richiesto'];
		}

		// Utilizza la funzione esistente per inserire l'acquisto
		$risultato = $this->inserisciMarketplaceAcquisto($dati_acquisto);

		if (strlen($dati['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		if (is_array($risultato) && isset($risultato['acquisti']) && !empty($risultato['acquisti'])) {
			return [
				'esito' => 'OK',
				'id_acquisto' => $risultato['acquisti'][0],
				'messaggio' => 'Articolo aggiunto al carrello con successo'
			];
		} else {
			return ['esito' => 'ERRORE', 'errori' => $risultato];
		}

		/*}}}*/
	}

	/**
	 * Funzione in DELETE
	 * Elimina un articolo dal carrello (soft delete)
	 *
	 * @param array $dati dati per l'eliminazione
	 * $dati['id_acquisto'] - ID dell'acquisto da eliminare (obbligatorio)
	 * $dati['id_studente'] - ID dello studente per verifica autorizzazione (obbligatorio)
	 * $dati['db_richiesto'] - database specifico (opzionale)
	 * @return array risultato operazione
	 */
	public function eliminaArticoloCarrello($dati) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($dati['id_acquisto']) || !is_numeric($dati['id_acquisto']) || $dati['id_acquisto'] <= 0) {
			$errori['id_acquisto'] = 'ID acquisto obbligatorio e deve essere numerico positivo';
		}

		if (!isset($dati['id_studente']) || !is_numeric($dati['id_studente']) || $dati['id_studente'] <= 0) {
			$errori['id_studente'] = 'ID studente obbligatorio e deve essere numerico positivo';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		if (strlen($dati['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($dati['db_richiesto']);
		}

		// Verifica che l'acquisto esista, appartenga allo studente e sia nel carrello
		$sql_verifica = "SELECT * FROM marketplace_studenti_acquisti
						WHERE id_acquisto = :id_acquisto
						  AND id_studente = :id_studente
						  AND stato_ordine = 'CARRELLO'
						  AND flag_canc = 0";

		$query_verifica = $this->user->data->db->prepare($sql_verifica);
		$query_verifica->bindParam(':id_acquisto', $dati['id_acquisto']);
		$query_verifica->bindParam(':id_studente', $dati['id_studente']);
		$query_verifica->execute();
		$acquisto = $query_verifica->fetch(PDO::FETCH_ASSOC);

		if (!$acquisto) {
			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return ['esito' => 'ERRORE', 'errori' => ['acquisto' => 'Acquisto non trovato nel carrello o non autorizzato']];
		}

		// Utilizza la funzione esistente per eliminare l'acquisto
		$risultato = $this->softDeleteItemAcquisti($dati);

		if (strlen($dati['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return $risultato;

		/*}}}*/
	}

	/**
	 * Funzione in GET
	 * Estrae gli articoli presenti nel carrello per uno studente
	 *
	 * @param array $filter parametri di filtro
	 * $filter['id_studente'] - ID dello studente (obbligatorio)
	 * $filter['db_richiesto'] - database specifico (opzionale)
	 * @return array lista articoli nel carrello
	 */
	public function estraiArticoliCarrello($filter) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($filter['id_studente']) || !is_numeric($filter['id_studente']) || $filter['id_studente'] <= 0) {
			$errori['id_studente'] = 'ID studente obbligatorio e deve essere numerico positivo';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		// Utilizza la funzione esistente con filtro per stato CARRELLO
		$filter_acquisti = [
			'id_studente' => $filter['id_studente'],
			'stato_ordine' => 'CARRELLO'
		];

		if (strlen($filter['db_richiesto']) > 0) {
			$filter_acquisti['db_richiesto'] = $filter['db_richiesto'];
		}

		$acquisti = $this->getListaMarketplaceAcquisti($filter_acquisti);

		return [
			'esito' => 'OK',
			'carrello' => $acquisti,
			'totale_articoli' => count($acquisti)
		];

		/*}}}*/
	}

	/**
	 * Funzione in GET
	 * Verifica la disponibilità degli articoli nel carrello prima del checkout
	 * Controlla validità, disponibilità e eventuali limitazioni
	 *
	 * @param array $filter parametri di filtro
	 * $filter['id_studente'] - ID dello studente (obbligatorio)
	 * $filter['db_richiesto'] - database specifico (opzionale)
	 * @return array risultato verifica disponibilità
	 */
	public function verificaDisponibilitaArticoliCarrello($filter) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($filter['id_studente']) || !is_numeric($filter['id_studente']) || $filter['id_studente'] <= 0) {
			$errori['id_studente'] = 'ID studente obbligatorio e deve essere numerico positivo';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		if (strlen($filter['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($filter['db_richiesto']);
		}

		// Estrai articoli nel carrello
		$carrello = $this->estraiArticoliCarrello($filter);

		if ($carrello['esito'] !== 'OK') {
			if (strlen($filter['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return $carrello;
		}

		$articoli_carrello = $carrello['carrello'];
		$timestamp_corrente = time();
		$articoli_disponibili = [];
		$articoli_non_disponibili = [];
		$totale_importo = 0;

		foreach ($articoli_carrello as $id_acquisto => $articolo) {
			$disponibile = true;
			$motivo_indisponibilita = '';

			// Verifica validità temporale dell'articolo marketplace
			$sql_marketplace = "SELECT * FROM marketplace
							   WHERE id_marketplace = :id_marketplace
							     AND flag_canc = 0";

			$query_marketplace = $this->user->data->db->prepare($sql_marketplace);
			$query_marketplace->bindParam(':id_marketplace', $articolo['id_marketplace']);
			$query_marketplace->execute();
			$marketplace_item = $query_marketplace->fetch(PDO::FETCH_ASSOC);

			if (!$marketplace_item) {
				$disponibile = false;
				$motivo_indisponibilita = 'Articolo non più disponibile';
			} else {
				// Verifica periodo di validità
				if ($marketplace_item['validita_inizio'] > $timestamp_corrente) {
					$disponibile = false;
					$motivo_indisponibilita = 'Articolo non ancora disponibile';
				} elseif ($marketplace_item['validita_fine'] != -1 && $marketplace_item['validita_fine'] < $timestamp_corrente) {
					$disponibile = false;
					$motivo_indisponibilita = 'Articolo scaduto';
				}

				// Verifica caratteristiche specifiche del negozio
				if ($disponibile && !empty($marketplace_item['caratteristiche'])) {
					$caratteristiche = json_decode($marketplace_item['caratteristiche'], true);
					if (isset($caratteristiche['oggetto_negozio'])) {
						$obj_negozio = $caratteristiche['oggetto_negozio'];

						// Calcola prezzo (con eventuale sconto)
						$prezzo = floatval($obj_negozio['prezzo_unitario'] ?? 0);
						if (isset($obj_negozio['prezzo_scontato']) && isset($obj_negozio['sconto_valido_dal']) && isset($obj_negozio['sconto_valido_al'])) {
							$sconto_dal = strtotime($obj_negozio['sconto_valido_dal']);
							$sconto_al = strtotime($obj_negozio['sconto_valido_al']);
							if ($timestamp_corrente >= $sconto_dal && $timestamp_corrente <= $sconto_al) {
								$prezzo = floatval($obj_negozio['prezzo_scontato']);
							}
						}

						$articolo['prezzo_finale'] = $prezzo;
						$totale_importo += $prezzo;
					}
				}
			}

			if ($disponibile) {
				$articoli_disponibili[$id_acquisto] = $articolo;
			} else {
				$articolo['motivo_indisponibilita'] = $motivo_indisponibilita;
				$articoli_non_disponibili[$id_acquisto] = $articolo;
			}
		}

		if (strlen($filter['db_richiesto']) > 0) {
			$this->user->data->SetDb($previous);
		}

		return [
			'esito' => 'OK',
			'articoli_disponibili' => $articoli_disponibili,
			'articoli_non_disponibili' => $articoli_non_disponibili,
			'totale_articoli_disponibili' => count($articoli_disponibili),
			'totale_articoli_non_disponibili' => count($articoli_non_disponibili),
			'totale_importo' => $totale_importo,
			'carrello_valido' => (count($articoli_non_disponibili) === 0)
		];

		/*}}}*/
	}

	/**
	 * Funzione in PUT
	 * Aggiorna il tipo movimento per un elemento marketplace
	 *
	 * @param array $dati dati per l'aggiornamento
	 * $dati['id_marketplace'] - ID dell'elemento marketplace (obbligatorio)
	 * $dati['id_tipo_movimento'] - nuovo ID tipo movimento (obbligatorio)
	 * $dati['db_richiesto'] - database specifico (opzionale)
	 * @return mixed ID marketplace aggiornato o array errori
	 */
	public function updateTipoMovimentoMarketplace($dati) {
		/*{{{ */

		$errori = [];

		// Validazione parametri obbligatori
		if (!isset($dati['id_marketplace']) || !is_numeric($dati['id_marketplace']) || $dati['id_marketplace'] <= 0) {
			$errori['id_marketplace'] = 'ID marketplace obbligatorio e deve essere numerico positivo';
		}

		if (!isset($dati['id_tipo_movimento']) || !is_numeric($dati['id_tipo_movimento'])) {
			$errori['id_tipo_movimento'] = 'ID tipo movimento obbligatorio e deve essere numerico';
		}

		if (count($errori) > 0) {
			return ['esito' => 'ERRORE', 'errori' => $errori];
		}

		if (strlen($dati['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($dati['db_richiesto']);
		}

		// Verifica che l'elemento marketplace esista
		$sql_verifica = "SELECT * FROM marketplace
						WHERE id_marketplace = :id_marketplace AND flag_canc = 0";

		$query_verifica = $this->user->data->db->prepare($sql_verifica);
		$query_verifica->bindParam(':id_marketplace', $dati['id_marketplace']);
		$query_verifica->execute();
		$marketplace_item = $query_verifica->fetch(PDO::FETCH_ASSOC);

		if (!$marketplace_item) {
			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return ['esito' => 'ERRORE', 'errori' => ['marketplace' => 'Elemento marketplace non trovato']];
		}

		// Aggiorna il tipo movimento
		$current_user = $this->user->getUserInfo()['id_utente'];
		$timestamp = time();

		$sql_update = "UPDATE marketplace
					  SET id_tipo_movimento = :id_tipo_movimento,
					      chi_modifica = :chi_modifica,
					      data_modifica = :data_modifica,
					      tipo_modifica = 'INTERFACCIA'
					  WHERE id_marketplace = :id_marketplace
					  RETURNING id_marketplace";

		$query_update = $this->user->data->db->prepare($sql_update);
		$query_update->bindParam(':id_tipo_movimento', $dati['id_tipo_movimento']);
		$query_update->bindParam(':chi_modifica', $current_user);
		$query_update->bindParam(':data_modifica', $timestamp);
		$query_update->bindParam(':id_marketplace', $dati['id_marketplace']);
		$query_update->execute();

		$risultato = $query_update->fetch(PDO::FETCH_ASSOC);

		if ($risultato && $risultato['id_marketplace']) {
			// Log dell'operazione
			$dati_log = [
				'id_marketplace' => $risultato['id_marketplace'],
				'id_tipo_movimento_precedente' => $marketplace_item['id_tipo_movimento'],
				'id_tipo_movimento_nuovo' => $dati['id_tipo_movimento']
			];

			$this->user->data->inserisciLog($dati_log, 'marketplace', $current_user, "NEXTAPI", "AGGIORNAMENTO_TIPO_MOVIMENTO");

			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}

			return [
				'esito' => 'OK',
				'id_marketplace' => $risultato['id_marketplace'],
				'messaggio' => 'Tipo movimento aggiornato con successo'
			];
		} else {
			if (strlen($dati['db_richiesto']) > 0) {
				$this->user->data->SetDb($previous);
			}
			return ['esito' => 'ERRORE', 'errori' => ['aggiornamento' => 'Errore durante l\'aggiornamento']];
		}

		/*}}}*/
	}

	/**
	 * Funzione in GET
	 * estrae tutti i record dalla tabella marketplace dove id_tipo_movimento è NULL o -1,
	 * e per ognuno verifica se esiste un record corrispondente nell'anno precedente con id_tipo_movimento popolato
	 *
	 * @param array $filter parametri di filtro opzionali
	 * @return array array di record con campo aggiuntivo id_tipo_movimento_anno_precedente
	 */
	public function getMarketplaceRecordsWithPreviousYear($filter = null) {
		/*{{{ */

		// Salva il database corrente
		$current_db = $this->user->data->GetCurrentDb();

		try {
			// Estrai i record dell'anno corrente con id_tipo_movimento NULL o -1
			$sql_current = "SELECT * FROM marketplace
							WHERE flag_canc = 0
							AND (id_tipo_movimento IS NULL OR id_tipo_movimento = -1)
							ORDER BY ordinamento, descrizione";

			$query_current = $this->user->data->db->prepare($sql_current);
			$query_current->execute();
			$records_current = $query_current->fetchAll(PDO::FETCH_ASSOC);

			if (empty($records_current)) {
				return [
					"esito" => "OK",
					"dati" => [],
					"messaggio" => "Nessun record trovato con id_tipo_movimento vuoto"
				];
			}

			// Ottieni informazioni sul database dell'anno precedente
			$prev_db_info = $this->user->data->GetPreviousDb($current_db);
			$prev_db_name = $prev_db_info['db'];

			// Verifica se il database dell'anno precedente esiste
			$elenco_db = array_column($this->user->data->GetListaDb(), 'nome');
			if (!in_array($prev_db_name, $elenco_db)) {
				// Se il database dell'anno precedente non esiste, restituisci i record senza il campo aggiuntivo
				$result_data = [];
				foreach ($records_current as $record) {
					$record['id_tipo_movimento_anno_precedente'] = null;
					$record['messaggio_anno_precedente'] = 'Database anno precedente non disponibile';
					$result_data[] = $record;
				}

				return [
					"esito" => "OK",
					"dati" => $result_data,
					"messaggio" => "Database anno precedente ({$prev_db_name}) non disponibile"
				];
			}

			// Connetti al database dell'anno precedente
			$previous_db = $this->user->data->SetDb($prev_db_name);

			$result_data = [];

			foreach ($records_current as $record) {
				$record['id_tipo_movimento_anno_precedente'] = null;
				$record['messaggio_anno_precedente'] = '';

				// Cerca record corrispondente nell'anno precedente usando id_marketplace
				$sql_prev = "SELECT id_tipo_movimento FROM marketplace
							WHERE flag_canc = 0
							AND id_marketplace = :id_marketplace
							AND id_tipo_movimento IS NOT NULL
							AND id_tipo_movimento != -1
							LIMIT 1";
				$query_prev = $this->user->data->db->prepare($sql_prev);
				$query_prev->execute([':id_marketplace' => $record['id_marketplace']]);
				$found_previous = $query_prev->fetch(PDO::FETCH_ASSOC);

				if ($found_previous) {
					$record['id_tipo_movimento_anno_precedente'] = $found_previous['id_tipo_movimento'];
					$record['messaggio_anno_precedente'] = 'Record corrispondente trovato nell\'anno precedente';
				} else {
					$record['messaggio_anno_precedente'] = 'Nessun record corrispondente trovato nell\'anno precedente';
				}

				$result_data[] = $record;
			}

			// Ripristina il database originale
			$this->user->data->SetDb($previous_db);

			return [
				"esito" => "OK",
				"dati" => $result_data,
				"messaggio" => "Elaborazione completata con successo"
			];

		} catch (Exception $e) {
			// Assicurati di ripristinare il database originale anche in caso di errore
			$this->user->data->SetDb($current_db);

			return [
				"esito" => "KO",
				"dati" => [],
				"messaggio" => "Errore durante l'elaborazione: " . $e->getMessage()
			];
		}
		/*}}}*/
	}

	/**
	 * Verifica che i record di marketplace abbiano un id_tipo_movimento
	 * che corrisponde ad un tipo movimento con school_year uguale o maggiore all'anno scolastico specificato
	 *
	 * @param array $filter parametri di filtro con anno_scolastico obbligatorio
	 * @return array risultato della verifica con dettagli sui record non validi
	 */
	public function verificaTipiMovimentoMarketplace($filter = null) {
		/*{{{ */

		require_once "models/class_school_basic.php";

		try {
			// Verifica che l'anno scolastico sia stato passato come parametro
			if (!isset($filter['anno_scolastico']) || empty($filter['anno_scolastico'])) {
				return [
					"esito" => "KO",
					"dati" => [],
					"messaggio" => "Parametro anno_scolastico obbligatorio non fornito. Formato richiesto: YYYY/YYYY (es. 2023/2024)"
				];
			}

			$anno_scolastico_da_verificare = $filter['anno_scolastico'];

			// Salva il database corrente
			$current_db = $this->user->data->GetCurrentDb();

			// Costruisci il nome del database per l'anno scolastico specificato
			$anno_db = str_replace('/', '_', $anno_scolastico_da_verificare);
			$nome_db = 'mastercom_' . $anno_db;

			// Connettiti al database dell'anno scolastico specificato
			$previous = $this->user->data->SetDb($nome_db);

			// Estrai tutti i record marketplace con id_tipo_movimento valorizzato
			$sql = "SELECT id_marketplace, descrizione, id_tipo_movimento, ordinamento
					FROM marketplace
					WHERE flag_canc = 0
					AND id_tipo_movimento IS NOT NULL
					AND id_tipo_movimento > 0
					ORDER BY ordinamento, descrizione";

			$query = $this->user->data->db->prepare($sql);
			$query->execute();
			$records_marketplace = $query->fetchAll(PDO::FETCH_ASSOC);

			if (empty($records_marketplace)) {
				// Ripristina il database originale
				$this->user->data->SetDb($current_db);

				return [
					"esito" => "OK",
					"dati" => [],
					"messaggio" => "Nessun record marketplace trovato con id_tipo_movimento valorizzato nel database {$nome_db}",
					"anno_scolastico_verificato" => $anno_scolastico_da_verificare,
					"database_utilizzato" => $nome_db
				];
			}

			$school = new School($this->user);
			$records_non_validi = [];
			$records_validi = [];
			$errori_chiamate = [];

			foreach ($records_marketplace as $record) {
				$id_tipo_movimento = intval($record['id_tipo_movimento']);

				// Chiama MC2 per ottenere informazioni sul tipo movimento
				$path = 'ccp/type/' . $id_tipo_movimento;
				$result = $school->callMc2($path, 'GET');

				if (is_array($result["results"]) && isset($result["results"]['school_year'])) {
					$school_year_tipo = $result["results"]['school_year'];

					// Verifica se l'anno scolastico del tipo movimento è uguale o maggiore a quello da verificare
					if ($this->confrontaAnniScolastici($school_year_tipo, $anno_scolastico_da_verificare) >= 0) {
						$records_validi[] = [
							'id_marketplace' => $record['id_marketplace'],
							'descrizione' => $record['descrizione'],
							'id_tipo_movimento' => $id_tipo_movimento,
							'school_year_tipo' => $school_year_tipo,
							'ordinamento' => $record['ordinamento'],
							'confronto' => $this->confrontaAnniScolastici($school_year_tipo, $anno_scolastico_da_verificare) === 0 ? 'uguale' : 'maggiore'
						];
					} else {
						$records_non_validi[] = [
							'id_marketplace' => $record['id_marketplace'],
							'descrizione' => $record['descrizione'],
							'id_tipo_movimento' => $id_tipo_movimento,
							'school_year_tipo' => $school_year_tipo,
							'anno_scolastico_verificato' => $anno_scolastico_da_verificare,
							'ordinamento' => $record['ordinamento'],
							'motivo' => 'Anno scolastico del tipo movimento è precedente a quello da verificare'
						];
					}
				} else {
					// Errore nella chiamata MC2 o tipo movimento non trovato
					$errori_chiamate[] = [
						'id_marketplace' => $record['id_marketplace'],
						'descrizione' => $record['descrizione'],
						'id_tipo_movimento' => $id_tipo_movimento,
						'ordinamento' => $record['ordinamento'],
						'motivo' => 'Errore nella chiamata MC2 o tipo movimento non trovato',
						'risposta_mc2' => $result
					];
				}
			}

			// Ripristina il database originale
			$this->user->data->SetDb($current_db);

			return [
				"esito" => "OK",
				"anno_scolastico_verificato" => $anno_scolastico_da_verificare,
				"database_utilizzato" => $nome_db,
				"totale_records_verificati" => count($records_marketplace),
				"records_validi" => $records_validi,
				"records_non_validi" => $records_non_validi,
				"errori_chiamate_mc2" => $errori_chiamate,
				"conteggi" => [
					"validi" => count($records_validi),
					"non_validi" => count($records_non_validi),
					"errori" => count($errori_chiamate)
				],
				"messaggio" => "Verifica completata con successo"
			];

		} catch (Exception $e) {
			// Assicurati di ripristinare il database originale anche in caso di errore
			$this->user->data->SetDb($current_db);

			return [
				"esito" => "KO",
				"dati" => [],
				"messaggio" => "Errore durante la verifica: " . $e->getMessage()
			];
		}
		/*}}}*/
	}

	/**
	 * Confronta due anni scolastici nel formato YYYY/YYYY
	 *
	 * @param string $anno1 Primo anno scolastico (es. "2023/2024")
	 * @param string $anno2 Secondo anno scolastico (es. "2022/2023")
	 * @return int -1 se anno1 < anno2, 0 se uguali, 1 se anno1 > anno2
	 */
	private function confrontaAnniScolastici($anno1, $anno2) {
		/*{{{ */
		// Estrai l'anno di inizio da entrambi gli anni scolastici
		$inizio_anno1 = intval(explode('/', $anno1)[0]);
		$inizio_anno2 = intval(explode('/', $anno2)[0]);

		if ($inizio_anno1 < $inizio_anno2) {
			return -1;
		} elseif ($inizio_anno1 > $inizio_anno2) {
			return 1;
		} else {
			return 0;
		}
		/*}}}*/
	}

}
?>
